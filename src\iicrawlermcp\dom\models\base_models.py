"""
Base DOM data models.

This module contains the fundamental data structures for DOM elements.
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any, List


@dataclass
class ElementInfo:
    """Represents information about a DOM element."""

    id: str
    tag_name: str
    xpath: str
    attributes: Dict[str, Any]
    text_content: str
    highlight_index: Optional[int] = None
    is_visible: bool = False
    is_interactive: bool = False
    is_in_viewport: bool = False
    children_ids: List[str] = None

    def __post_init__(self):
        if self.children_ids is None:
            self.children_ids = []
