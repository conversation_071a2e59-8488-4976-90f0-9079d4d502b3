"""
DOM element filtering utilities.

This module provides utilities for filtering and selecting DOM elements.
"""

from typing import List, Callable, Any, Dict
from ..models.base_models import ElementInfo


class ElementFilter:
    """元素过滤工具"""

    @staticmethod
    def filter_visible_elements(elements: List[ElementInfo]) -> List[ElementInfo]:
        """
        过滤可见元素

        Args:
            elements: 元素列表

        Returns:
            可见元素列表
        """
        return [elem for elem in elements if elem.is_visible]

    @staticmethod
    def filter_interactive_elements(elements: List[ElementInfo]) -> List[ElementInfo]:
        """
        过滤交互元素

        Args:
            elements: 元素列表

        Returns:
            可交互元素列表
        """
        return [elem for elem in elements if elem.is_interactive]

    @staticmethod
    def filter_in_viewport_elements(elements: List[ElementInfo]) -> List[ElementInfo]:
        """
        过滤视口内元素

        Args:
            elements: 元素列表

        Returns:
            视口内元素列表
        """
        return [elem for elem in elements if elem.is_in_viewport]

    @staticmethod
    def filter_by_tag_name(
        elements: List[ElementInfo], tag_names: List[str]
    ) -> List[ElementInfo]:
        """
        按标签名过滤元素

        Args:
            elements: 元素列表
            tag_names: 标签名列表

        Returns:
            匹配标签名的元素列表
        """
        tag_names_lower = [tag.lower() for tag in tag_names]
        return [elem for elem in elements if elem.tag_name.lower() in tag_names_lower]

    @staticmethod
    def filter_by_text_content(
        elements: List[ElementInfo],
        text: str,
        case_sensitive: bool = False,
        exact_match: bool = False,
    ) -> List[ElementInfo]:
        """
        按文本内容过滤元素

        Args:
            elements: 元素列表
            text: 要搜索的文本
            case_sensitive: 是否区分大小写
            exact_match: 是否精确匹配

        Returns:
            匹配文本的元素列表
        """
        if not text:
            return elements

        search_text = text if case_sensitive else text.lower()
        filtered_elements = []

        for elem in elements:
            element_text = (
                elem.text_content if case_sensitive else elem.text_content.lower()
            )

            if exact_match:
                if element_text == search_text:
                    filtered_elements.append(elem)
            else:
                if search_text in element_text:
                    filtered_elements.append(elem)

        return filtered_elements

    @staticmethod
    def filter_by_attributes(
        elements: List[ElementInfo], attribute_filters: Dict[str, Any]
    ) -> List[ElementInfo]:
        """
        按属性过滤元素

        Args:
            elements: 元素列表
            attribute_filters: 属性过滤条件字典

        Returns:
            匹配属性条件的元素列表
        """
        filtered_elements = []

        for elem in elements:
            match = True
            for attr_name, expected_value in attribute_filters.items():
                actual_value = elem.attributes.get(attr_name)

                if expected_value is None:
                    # 检查属性是否存在
                    if actual_value is None:
                        match = False
                        break
                elif isinstance(expected_value, str):
                    # 字符串匹配（支持部分匹配）
                    if actual_value is None or expected_value not in str(actual_value):
                        match = False
                        break
                else:
                    # 精确匹配
                    if actual_value != expected_value:
                        match = False
                        break

            if match:
                filtered_elements.append(elem)

        return filtered_elements

    @staticmethod
    def filter_by_xpath_pattern(
        elements: List[ElementInfo], xpath_pattern: str
    ) -> List[ElementInfo]:
        """
        按XPath模式过滤元素

        Args:
            elements: 元素列表
            xpath_pattern: XPath模式（支持简单的通配符）

        Returns:
            匹配XPath模式的元素列表
        """
        import re

        # 将简单的通配符转换为正则表达式
        pattern = xpath_pattern.replace("*", ".*").replace("?", ".")
        regex = re.compile(pattern)

        return [elem for elem in elements if regex.search(elem.xpath)]

    @staticmethod
    def filter_by_custom_condition(
        elements: List[ElementInfo], condition: Callable[[ElementInfo], bool]
    ) -> List[ElementInfo]:
        """
        按自定义条件过滤元素

        Args:
            elements: 元素列表
            condition: 自定义条件函数

        Returns:
            满足条件的元素列表
        """
        return [elem for elem in elements if condition(elem)]

    @staticmethod
    def sort_elements(
        elements: List[ElementInfo], sort_key: str = "xpath", reverse: bool = False
    ) -> List[ElementInfo]:
        """
        排序元素

        Args:
            elements: 元素列表
            sort_key: 排序键 ('xpath', 'tag_name', 'text_content', 'highlight_index')
            reverse: 是否逆序

        Returns:
            排序后的元素列表
        """
        if sort_key == "xpath":
            return sorted(elements, key=lambda x: x.xpath, reverse=reverse)
        elif sort_key == "tag_name":
            return sorted(elements, key=lambda x: x.tag_name, reverse=reverse)
        elif sort_key == "text_content":
            return sorted(elements, key=lambda x: x.text_content, reverse=reverse)
        elif sort_key == "highlight_index":
            return sorted(
                elements, key=lambda x: x.highlight_index or 0, reverse=reverse
            )
        else:
            return elements

    @staticmethod
    def limit_elements(elements: List[ElementInfo], limit: int) -> List[ElementInfo]:
        """
        限制元素数量

        Args:
            elements: 元素列表
            limit: 最大数量

        Returns:
            限制数量后的元素列表
        """
        return elements[:limit] if limit > 0 else elements

    @staticmethod
    def group_elements_by_tag(
        elements: List[ElementInfo],
    ) -> Dict[str, List[ElementInfo]]:
        """
        按标签名分组元素

        Args:
            elements: 元素列表

        Returns:
            按标签名分组的元素字典
        """
        groups = {}
        for elem in elements:
            tag_name = elem.tag_name.lower()
            if tag_name not in groups:
                groups[tag_name] = []
            groups[tag_name].append(elem)

        return groups

    @staticmethod
    def get_element_statistics(elements: List[ElementInfo]) -> Dict[str, Any]:
        """
        获取元素统计信息

        Args:
            elements: 元素列表

        Returns:
            统计信息字典
        """
        if not elements:
            return {
                "total": 0,
                "visible": 0,
                "interactive": 0,
                "in_viewport": 0,
                "by_tag": {},
            }

        visible_count = sum(1 for elem in elements if elem.is_visible)
        interactive_count = sum(1 for elem in elements if elem.is_interactive)
        in_viewport_count = sum(1 for elem in elements if elem.is_in_viewport)

        tag_counts = {}
        for elem in elements:
            tag_name = elem.tag_name.lower()
            tag_counts[tag_name] = tag_counts.get(tag_name, 0) + 1

        return {
            "total": len(elements),
            "visible": visible_count,
            "interactive": interactive_count,
            "in_viewport": in_viewport_count,
            "by_tag": tag_counts,
        }
