"""
CodeGen Agent module for generating Playwright automation code.

这个模块提供专业的代码生成Agent，能够将Web自动化操作历史转换为
高质量、生产就绪的Playwright Python代码。遵循项目的Agent架构模式。
"""

import logging
from typing import Optional, List
from .prompts import get_codegen_agent_prompt
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain_openai import ChatOpenAI
from langchain_core.tools import BaseTool

from ..core.config import config

logger = logging.getLogger(__name__)


class CodeGenAgent:
    """
    代码生成专家Agent。
    
    专门将Web自动化操作历史转换为Playwright Python代码的智能Agent。

    特性：
    - 使用专业的代码生成工具集
    - 支持从执行历史和任务描述生成代码
    - 集成验证和修复功能
    - 自动保存生成的代码
    """

    def __init__(
        self,
        tools: Optional[List[BaseTool]] = None,
        verbose: Optional[bool] = None,
        llm_config: Optional[dict] = None,
    ):
        """
        初始化CodeGenAgent。

        Args:
            tools: LangChain工具列表。如果为None，使用默认的代码生成工具。
            verbose: 是否启用详细日志。
            llm_config: 自定义LLM配置。
        """
        if tools is None:
            from ..tools.codegen_tools import get_codegen_tools

            self.tools = get_codegen_tools()
            logger.info(
                f"CodeGenAgent initialized with {len(self.tools)} tools: "
                f"extract_and_generate_code, yolo_validate_and_fix, save_generated_code"
            )
        else:
            self.tools = tools

        self.verbose = verbose if verbose is not None else config.VERBOSE
        self.llm_config = llm_config or config.get_llm_config()

        self._llm = None
        self._agent = None
        self._executor = None

    def _create_llm(self) -> ChatOpenAI:
        """创建和配置LLM实例。"""
        if self._llm is None:
            try:
                self._llm = ChatOpenAI(**self.llm_config)
                logger.info(f"LLM created with model: {self.llm_config.get('model')}")
            except Exception as e:
                logger.error(f"Failed to create LLM: {e}")
                raise
        return self._llm

    def _create_agent(self) -> None:
        """创建LangChain agent。"""
        if self._agent is None:
            try:
                llm = self._create_llm()
                prompt = get_codegen_agent_prompt()
                self._agent = create_openai_functions_agent(llm, self.tools, prompt)
                logger.info("CodeGenAgent created successfully")
            except Exception as e:
                logger.error(f"Failed to create agent: {e}")
                raise

    def _create_executor(self) -> AgentExecutor:
        """创建agent executor。"""
        if self._executor is None:
            self._create_agent()
            try:
                self._executor = AgentExecutor(
                    agent=self._agent,
                    tools=self.tools,
                    verbose=self.verbose,
                    max_iterations=10,
                    handle_parsing_errors=True,
                )
                logger.info("CodeGenAgent executor created successfully")
            except Exception as e:
                logger.error(f"Failed to create agent executor: {e}")
                raise
        return self._executor

    def invoke(self, input_text: str) -> dict:
        """
        调用Agent执行代码生成任务。

        Args:
            input_text: 包含执行历史或代码生成请求的文本

        Returns:
            包含代码生成结果的字典

        Examples:
            # 使用工具生成代码
            result = agent.invoke("请使用extract_and_generate_code工具处理执行历史...")

            # 使用工具验证代码
            result = agent.invoke("请使用yolo_validate_and_fix工具验证代码...")
        """
        executor = self._create_executor()

        try:
            logger.info(f"CodeGenAgent starting code generation for: {input_text[:100]}...")
            result = executor.invoke({"input": input_text})
            logger.info("CodeGenAgent code generation completed successfully")
            return result
        except Exception as e:
            logger.error(f"CodeGenAgent code generation failed: {e}")
            raise

    def cleanup(self) -> None:
        """清理Agent使用的资源。"""
        try:
            self._llm = None
            self._agent = None
            self._executor = None
            logger.info("CodeGenAgent cleanup completed")
        except Exception as e:
            logger.error(f"Error during CodeGenAgent cleanup: {e}")


def build_codegen_agent(
    tools: Optional[List[BaseTool]] = None,
    verbose: Optional[bool] = None,
    llm_config: Optional[dict] = None,
) -> CodeGenAgent:
    """
    构建并返回配置好的CodeGenAgent实例。

    Args:
        tools: LangChain工具列表
        verbose: 是否启用详细日志
        llm_config: 自定义LLM配置

    Returns:
        配置好的CodeGenAgent实例
        
    Example:
        # 创建默认配置的CodeGenAgent
        agent = build_codegen_agent()
        
        # 创建自定义配置的CodeGenAgent
        agent = build_codegen_agent(verbose=True, llm_config={"temperature": 0.1})
    """
    try:
        agent = CodeGenAgent(tools=tools, verbose=verbose, llm_config=llm_config)
        logger.info("CodeGenAgent built successfully")
        return agent
    except Exception as e:
        logger.error(f"Failed to build CodeGenAgent: {e}")
        raise


# 模块导出信息
__all__ = [
    "CodeGenAgent",
    "build_codegen_agent"
]