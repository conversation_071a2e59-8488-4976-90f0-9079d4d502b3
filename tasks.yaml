# 任务配置文件
# 在这里定义你的自动化任务

tasks:
  - name: "百度搜索Python"
    description: "访问百度并搜索Python相关内容"
    steps:
      - action: navigate
        url: "https://www.baidu.com"
      - action: type
        selector: 'input[name="wd"]'
        text: "Python教程"
      - action: click
        selector: 'input[type="submit"]'
      - action: wait
        selector: 'div#content_left'
      - action: screenshot
        filename: "baidu_result.png"

  - name: "GitHub项目搜索"
    description: "在GitHub搜索开源项目"
    steps:
      - action: navigate
        url: "https://github.com"
      - action: type
        selector: 'input[name="q"]'
        text: "langchain python"
      - action: press_key
        key: "Enter"
      - action: wait
        selector: 'div.repo-list'
      - action: click
        selector: 'div.repo-list article:first-child h3 a'
      - action: screenshot
        filename: "github_project.png"

  - name: "电商购物流程"
    description: "模拟电商网站购物流程"
    steps:
      - action: navigate
        url: "https://www.amazon.com"
      - action: type
        selector: 'input#twotabsearchtextbox'
        text: "mechanical keyboard"
      - action: click
        selector: 'input#nav-search-submit-button'
      - action: wait
        selector: 'div[data-component-type="s-search-result"]'
      - action: click
        selector: 'div[data-component-type="s-search-result"]:first-child h2 a'
      - action: wait
        selector: 'input#add-to-cart-button'
      - action: screenshot
        filename: "product_page.png"

  - name: "表单填写测试"
    description: "自动填写Web表单"
    steps:
      - action: navigate
        url: "https://example.com/form"
      - action: type
        selector: 'input#name'
        text: "张三"
      - action: type
        selector: 'input#email'
        text: "<EMAIL>"
      - action: type
        selector: 'input#phone'
        text: "13800138000"
      - action: select
        selector: 'select#country'
        value: "China"
      - action: click
        selector: 'input#agree'
      - action: click
        selector: 'button[type="submit"]'
      - action: wait
        selector: 'div.success-message'
      - action: screenshot
        filename: "form_submitted.png"