"""
Prompt optimization tools for iICrawlerMCP.

Pure LLM-driven approach: Let the large model optimize the large model.
No rules, no templates, just intelligent understanding and optimization.
"""

import logging
from langchain_core.tools import tool
from typing import List, Optional
from langchain_core.tools import BaseTool

logger = logging.getLogger(__name__)


@tool
def generate_standard_template(user_prompt: str) -> str:
    """
    让LLM生成理想的标准提示词模板。
    
    不使用预定义模板，而是让LLM根据用户输入实时生成最合适的模板。
    LLM会理解任务本质，生成包含所有必要信息的理想模板。
    
    Args:
        user_prompt: 用户的原始输入
        
    Returns:
        LLM生成的理想提示词模板
        
    Example:
        用户输入："爬淘宝数据"
        生成模板："导航到taobao.com，搜索[商品类目]，提取前[N]个商品的标题、价格、销量、评分，保存为[格式]文件"
    """
    try:
        from ..core.config import config
        from langchain_openai import ChatOpenAI
        
        logger.info(f"Generating standard template for: {user_prompt[:50]}...")
        
        llm = ChatOpenAI(**config.get_llm_config())
        
        template_prompt = f"""
        作为提示词优化专家，请为以下用户输入生成一个理想的标准提示词模板。
        
        用户输入："{user_prompt}"
        
        要求：
        1. 理解用户的真实意图
        2. 生成一个完整、清晰、可执行的提示词模板
        3. 包含所有必要的信息点（用[]标记需要填充的部分）
        4. 结构化、步骤化表达
        5. 不要解释，直接输出模板
        
        示例格式：
        目标：[具体目标]
        步骤：
        1. [第一步操作]
        2. [第二步操作]
        输出：[期望的输出格式]
        
        请生成理想的提示词模板：
        """
        
        result = llm.invoke(template_prompt)
        logger.info("Standard template generated successfully")
        return result.content
        
    except Exception as e:
        error_msg = f"Template generation failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


@tool
def search_web_context(user_prompt: str) -> str:
    """
    智能搜索网络，补充提示词所需的上下文信息。
    
    LLM会分析用户输入，决定需要搜索什么信息：
    - 网站域名（如"淘宝"→taobao.com）
    - 专业术语解释
    - 最佳实践和方法
    - 最新的技术信息
    
    Args:
        user_prompt: 用户的原始输入
        
    Returns:
        搜索到的相关信息
        
    Example:
        用户输入："爬取京东商品数据"
        搜索内容：京东官网域名、京东API文档、网页爬虫最佳实践等
    """
    try:
        from ..core.config import config
        from langchain_openai import ChatOpenAI
        from .search_tools import web_search_serp
        
        logger.info(f"Analyzing what to search for: {user_prompt[:50]}...")
        
        llm = ChatOpenAI(**config.get_llm_config())
        
        # 让LLM决定需要搜索什么
        analysis_prompt = f"""
        分析以下用户输入，识别需要从网络搜索的信息。
        
        用户输入："{user_prompt}"
        
        请识别并列出需要搜索的内容（最多3个）：
        1. 如果提到网站名称但没有URL，需要搜索其官方域名
        2. 如果有专业术语，需要搜索其含义和用法
        3. 如果是技术任务，需要搜索最佳实践
        
        直接输出需要搜索的查询词，每行一个，不要解释。
        如果不需要搜索，返回"无需搜索"。
        """
        
        result = llm.invoke(analysis_prompt)
        search_queries = result.content.strip().split('\n')
        
        if "无需搜索" in result.content:
            return "无需额外搜索信息"
        
        # 执行搜索
        all_results = []
        for query in search_queries[:3]:  # 最多搜索3个
            if query and len(query) > 2:
                logger.info(f"Searching for: {query}")
                search_result = web_search_serp({"query": query.strip(), "num_results": 3})
                all_results.append(f"搜索：{query}\n{search_result}")
        
        combined_results = "\n\n".join(all_results) if all_results else "未找到相关信息"
        logger.info(f"Web context search completed, found {len(all_results)} results")
        
        return combined_results
        
    except Exception as e:
        error_msg = f"Web context search failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


@tool
def generate_clarifying_questions(user_prompt: str, template: str, web_info: Optional[str] = None) -> str:
    """
    生成澄清问题，补充缺失的关键信息。
    
    LLM会对比用户输入和理想模板，识别缺失的必要信息，
    生成最少但必要的澄清问题。
    
    Args:
        user_prompt: 用户的原始输入
        template: 理想的提示词模板
        web_info: 从网络搜索到的信息（可选）
        
    Returns:
        需要向用户询问的澄清问题
        
    Example:
        返回："请回答以下问题以完善任务：
        1. 您想搜索什么类型的商品？
        2. 需要获取多少条数据？"
    """
    try:
        from ..core.config import config
        from langchain_openai import ChatOpenAI
        
        logger.info("Generating clarifying questions...")
        
        llm = ChatOpenAI(**config.get_llm_config())
        
        question_prompt = f"""
        作为提示词优化专家，请生成必要的澄清问题。
        
        用户输入："{user_prompt}"
        理想模板：{template}
        {f"网络信息：{web_info[:500]}" if web_info else ""}
        
        对比用户输入和理想模板，识别缺失的关键信息。
        
        要求：
        1. 只问真正必要的问题（最多3个）
        2. 问题要具体、清晰
        3. 如果网络信息已经提供了答案，不要重复询问
        4. 如果信息已经足够，返回"信息完整，无需澄清"
        
        输出格式：
        为了更好地完成您的任务，请回答以下问题：
        1. [问题1]
        2. [问题2]
        
        请生成澄清问题：
        """
        
        result = llm.invoke(question_prompt)
        logger.info("Clarifying questions generated")
        return result.content
        
    except Exception as e:
        error_msg = f"Question generation failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


@tool
def generate_optimized_prompt(
    user_prompt: str,
    template: str,
    web_info: Optional[str] = None,
    user_answers: Optional[str] = None
) -> str:
    """
    生成最终的优化提示词。
    
    整合所有信息（原始输入、理想模板、网络信息、用户回答），
    生成清晰、完整、可执行的优化提示词。
    
    Args:
        user_prompt: 用户的原始输入
        template: 理想的提示词模板
        web_info: 从网络搜索到的信息（可选）
        user_answers: 用户对澄清问题的回答（可选）
        
    Returns:
        优化后的提示词
        
    Example:
        输出："目标：爬取京东平台的手机商品数据
        网站：jd.com
        搜索关键词：智能手机
        数据要求：
        - 提取前20个商品
        - 字段：标题、价格、销量、评分、商品链接
        输出格式：JSON文件
        保存路径：jd_phones_[日期].json"
    """
    try:
        from ..core.config import config
        from langchain_openai import ChatOpenAI
        
        logger.info("Generating optimized prompt...")
        
        llm = ChatOpenAI(**config.get_llm_config())
        
        optimization_prompt = f"""
        作为提示词优化专家，请生成最终的优化提示词。
        
        原始输入："{user_prompt}"
        参考模板：{template}
        {f"网络信息：{web_info[:1000]}" if web_info else ""}
        {f"用户补充：{user_answers}" if user_answers else ""}
        
        整合所有信息，生成最优的提示词。
        
        要求：
        1. 保持用户的核心意图不变
        2. 结构清晰，信息完整
        3. 可直接执行，不含歧义
        4. 包含具体的参数和要求
        5. 简洁但不失完整性
        
        直接输出优化后的提示词，不要解释：
        """
        
        result = llm.invoke(optimization_prompt)
        
        optimized = result.content
        
        # 添加优化标记
        final_result = f"✨ **优化后的提示词**：\n\n{optimized}\n\n"
        final_result += f"📝 **原始输入**：{user_prompt}\n"
        final_result += "✅ **优化完成**：提示词已优化为更清晰、完整、可执行的版本"
        
        logger.info("Prompt optimization completed successfully")
        return final_result
        
    except Exception as e:
        error_msg = f"Prompt optimization failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


# 提示词优化工具列表（精简为4个核心工具）
PROMPT_OPTIMIZATION_TOOLS = [
    generate_standard_template,
    search_web_context,
    generate_clarifying_questions,
    generate_optimized_prompt,
]


def get_prompt_optimization_tools() -> List[BaseTool]:
    """
    Get prompt optimization tools for PromptOptimizationAgent.
    
    Returns:
        List of 4 core LLM-driven prompt optimization tools
    """
    return PROMPT_OPTIMIZATION_TOOLS.copy()