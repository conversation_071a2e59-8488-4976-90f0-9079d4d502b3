"""
DOM output models.

This module contains models for DOM extraction outputs and results.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from .base_models import ElementInfo


class NavigationElement(BaseModel):
    """导航元素专用模型"""

    highlight_index: Optional[int] = Field(description="元素高亮索引")
    text_content: str = Field(description="元素文本内容")
    xpath: str = Field(description="元素XPath路径")
    tag_name: str = Field(description="HTML标签名")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="元素属性")
    is_visible: bool = Field(description="是否可见")
    is_interactive: bool = Field(default=False, description="是否可交互")
    is_in_viewport: bool = Field(default=False, description="是否在视口内")
    nav_type: str = Field(
        description="导航类型"
    )  # 'menu', 'breadcrumb', 'pagination', etc.
    level: int = Field(default=1, description="导航层级")

    @classmethod
    def from_element_info(cls, elem) -> "NavigationElement":
        """从ElementInfo创建NavigationElement"""
        # 确定导航类型
        nav_type = "menu"  # 默认类型
        class_name = elem.attributes.get("class", "").lower()
        id_name = elem.attributes.get("id", "").lower()
        combined = class_name + " " + id_name

        if any(keyword in combined for keyword in ["breadcrumb", "面包屑"]):
            nav_type = "breadcrumb"
        elif any(keyword in combined for keyword in ["pagination", "pager", "分页"]):
            nav_type = "pagination"
        elif any(keyword in combined for keyword in ["tab", "标签"]):
            nav_type = "tabs"
        elif any(keyword in combined for keyword in ["sidebar", "侧边"]):
            nav_type = "sidebar"

        # 确定层级（基于XPath深度）
        level = len([part for part in elem.xpath.split("/") if part.strip()])

        return cls(
            highlight_index=elem.highlight_index,
            text_content=elem.text_content or "",
            xpath=elem.xpath,
            tag_name=elem.tag_name,
            attributes=elem.attributes,
            is_visible=elem.is_visible,
            is_interactive=elem.is_interactive,
            is_in_viewport=getattr(elem, "is_in_viewport", False),
            nav_type=nav_type,
            level=min(level, 10),  # 限制最大层级
        )


class FormElement(BaseModel):
    """表单元素专用模型"""

    highlight_index: Optional[int] = Field(description="元素高亮索引")
    text_content: str = Field(description="元素文本内容")
    xpath: str = Field(description="元素XPath路径")
    tag_name: str = Field(description="HTML标签名")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="元素属性")
    is_visible: bool = Field(description="是否可见")
    is_interactive: bool = Field(default=False, description="是否可交互")
    is_in_viewport: bool = Field(default=False, description="是否在视口内")
    action: str = Field(default="", description="表单提交地址")
    method: str = Field(default="GET", description="提交方法")
    enctype: str = Field(default="", description="编码类型")
    input_count: int = Field(default=0, description="输入框数量")
    button_count: int = Field(default=0, description="按钮数量")

    @classmethod
    def from_element_info(cls, elem) -> "FormElement":
        """从ElementInfo创建FormElement"""
        return cls(
            highlight_index=elem.highlight_index,
            text_content=elem.text_content or "",
            xpath=elem.xpath,
            tag_name=elem.tag_name,
            attributes=elem.attributes,
            is_visible=elem.is_visible,
            is_interactive=elem.is_interactive,
            is_in_viewport=getattr(elem, "is_in_viewport", False),
            action=elem.attributes.get("action", ""),
            method=elem.attributes.get("method", "GET").upper(),
            enctype=elem.attributes.get("enctype", ""),
            # 这些计数需要在实际使用时计算
            input_count=0,
            button_count=0,
        )


class MediaElement(BaseModel):
    """媒体元素专用模型（图片、视频、音频）"""

    highlight_index: Optional[int] = Field(description="元素高亮索引")
    text_content: str = Field(description="元素文本内容")
    xpath: str = Field(description="元素XPath路径")
    tag_name: str = Field(description="HTML标签名")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="元素属性")
    is_visible: bool = Field(description="是否可见")
    is_interactive: bool = Field(default=False, description="是否可交互")
    is_in_viewport: bool = Field(default=False, description="是否在视口内")
    src: str = Field(default="", description="媒体源地址")
    alt: str = Field(default="", description="替代文本")
    media_type: str = Field(description="媒体类型")  # 'image', 'video', 'audio'

    @classmethod
    def from_element_info(cls, elem) -> "MediaElement":
        """从ElementInfo创建MediaElement"""
        # 确定媒体类型
        tag_name = elem.tag_name.lower()
        if tag_name == "img":
            media_type = "image"
        elif tag_name == "video":
            media_type = "video"
        elif tag_name == "audio":
            media_type = "audio"
        else:
            media_type = "unknown"

        return cls(
            highlight_index=elem.highlight_index,
            text_content=elem.text_content or "",
            xpath=elem.xpath,
            tag_name=elem.tag_name,
            attributes=elem.attributes,
            is_visible=elem.is_visible,
            is_interactive=elem.is_interactive,
            is_in_viewport=getattr(elem, "is_in_viewport", False),
            src=elem.attributes.get("src", ""),
            alt=elem.attributes.get("alt", ""),
            media_type=media_type,
        )


class ExtractionResult(BaseModel):
    """DOM提取结果模型"""

    elements: List[ElementInfo] = Field(
        default_factory=list, description="提取的元素列表"
    )
    total_count: int = Field(description="元素总数")
    visible_count: int = Field(description="可见元素数")
    interactive_count: int = Field(description="可交互元素数")
    extraction_time: float = Field(description="提取耗时（秒）")
    page_url: str = Field(default="", description="页面URL")
    page_title: str = Field(default="", description="页面标题")
    viewport_size: Dict[str, int] = Field(default_factory=dict, description="视口大小")

    @classmethod
    def create(
        cls,
        elements: List[ElementInfo],
        extraction_time: float = 0.0,
        page_url: str = "",
        page_title: str = "",
        viewport_size: Dict[str, int] = None,
    ) -> "ExtractionResult":
        """创建提取结果"""
        if viewport_size is None:
            viewport_size = {}

        visible_count = sum(1 for elem in elements if elem.is_visible)
        interactive_count = sum(1 for elem in elements if elem.is_interactive)

        return cls(
            elements=elements,
            total_count=len(elements),
            visible_count=visible_count,
            interactive_count=interactive_count,
            extraction_time=extraction_time,
            page_url=page_url,
            page_title=page_title,
            viewport_size=viewport_size,
        )
