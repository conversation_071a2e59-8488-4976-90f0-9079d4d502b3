"""
DOM cache management.

This module provides caching functionality for DOM extraction results.
"""

import json
import os
import time
from typing import Dict, Any, Optional, List
from ..models.base_models import ElementInfo


class CacheManager:
    """专门的缓存管理器"""

    def __init__(self):
        """初始化缓存管理器"""
        self._element_cache: Dict[str, Any] = {}
        self._last_extraction_result: Optional[Dict[str, Any]] = None
        self._cache_file_path = os.path.join(
            os.path.dirname(__file__), "..", "elements.json"
        )
        self._cache_expiry_time = 300  # 5分钟缓存过期时间

    def get_cached_elements(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存的元素

        Args:
            cache_key: 缓存键

        Returns:
            缓存的元素数据，如果不存在或过期则返回None
        """
        if cache_key not in self._element_cache:
            return None

        cache_entry = self._element_cache[cache_key]

        # 检查缓存是否过期
        if time.time() - cache_entry.get("timestamp", 0) > self._cache_expiry_time:
            del self._element_cache[cache_key]
            return None

        return cache_entry.get("data")

    def cache_elements(
        self,
        cache_key: str,
        elements: List[ElementInfo],
        additional_data: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        缓存元素

        Args:
            cache_key: 缓存键
            elements: 要缓存的元素列表
            additional_data: 额外的数据
        """
        cache_data = {
            "elements": [self._element_to_dict(elem) for elem in elements],
            "count": len(elements),
            "timestamp": time.time(),
        }

        if additional_data:
            cache_data.update(additional_data)

        self._element_cache[cache_key] = {"data": cache_data, "timestamp": time.time()}

    def get_last_extraction_result(self) -> Optional[Dict[str, Any]]:
        """获取最后一次提取结果"""
        return self._last_extraction_result

    def set_last_extraction_result(self, result: Dict[str, Any]) -> None:
        """设置最后一次提取结果"""
        self._last_extraction_result = result

    def clear_cache(self) -> None:
        """清空所有缓存"""
        self._element_cache.clear()
        self._last_extraction_result = None

    def clear_expired_cache(self) -> int:
        """
        清理过期缓存

        Returns:
            清理的缓存条目数量
        """
        current_time = time.time()
        expired_keys = []

        for key, cache_entry in self._element_cache.items():
            if current_time - cache_entry.get("timestamp", 0) > self._cache_expiry_time:
                expired_keys.append(key)

        for key in expired_keys:
            del self._element_cache[key]

        return len(expired_keys)

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            缓存统计信息
        """
        current_time = time.time()
        valid_entries = 0
        expired_entries = 0
        total_elements = 0

        for cache_entry in self._element_cache.values():
            if (
                current_time - cache_entry.get("timestamp", 0)
                <= self._cache_expiry_time
            ):
                valid_entries += 1
                total_elements += cache_entry.get("data", {}).get("count", 0)
            else:
                expired_entries += 1

        return {
            "total_entries": len(self._element_cache),
            "valid_entries": valid_entries,
            "expired_entries": expired_entries,
            "total_cached_elements": total_elements,
            "cache_expiry_time": self._cache_expiry_time,
        }

    def load_persistent_cache(self) -> bool:
        """
        从文件加载持久化缓存

        Returns:
            是否成功加载
        """
        try:
            if os.path.exists(self._cache_file_path):
                with open(self._cache_file_path, "r", encoding="utf-8") as f:
                    content = f.read().strip()
                    if content:
                        persistent_data = json.loads(content)
                        # 这里可以根据需要处理持久化数据
                        return True
            return False
        except Exception:
            return False

    def save_persistent_cache(self, data: Dict[str, Any]) -> bool:
        """
        保存持久化缓存到文件

        Args:
            data: 要保存的数据

        Returns:
            是否成功保存
        """
        try:
            with open(self._cache_file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, separators=(",", ":"))
            return True
        except Exception:
            return False

    def _element_to_dict(self, element: ElementInfo) -> Dict[str, Any]:
        """
        将ElementInfo转换为字典

        Args:
            element: ElementInfo实例

        Returns:
            元素的字典表示
        """
        return {
            "id": element.id,
            "tag_name": element.tag_name,
            "xpath": element.xpath,
            "attributes": element.attributes,
            "text_content": element.text_content,
            "highlight_index": element.highlight_index,
            "is_visible": element.is_visible,
            "is_interactive": element.is_interactive,
            "is_in_viewport": element.is_in_viewport,
            "children_ids": element.children_ids,
        }

    def _dict_to_element(self, data: Dict[str, Any]) -> ElementInfo:
        """
        将字典转换为ElementInfo

        Args:
            data: 元素的字典表示

        Returns:
            ElementInfo实例
        """
        return ElementInfo(
            id=data.get("id", ""),
            tag_name=data.get("tag_name", ""),
            xpath=data.get("xpath", ""),
            attributes=data.get("attributes", {}),
            text_content=data.get("text_content", ""),
            highlight_index=data.get("highlight_index"),
            is_visible=data.get("is_visible", False),
            is_interactive=data.get("is_interactive", False),
            is_in_viewport=data.get("is_in_viewport", False),
            children_ids=data.get("children_ids", []),
        )
