"""
Direct Python API launcher for LangGraph Studio
This uses the LangGraph API directly without CLI
"""

import os
import sys
import asyncio
import uvicorn
from pathlib import Path

# Set working directory to project root
project_root = Path(__file__).parent
os.chdir(project_root)

# Add project to path
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
env_file = project_root / ".env"
if env_file.exists():
    load_dotenv(env_file)


def run_langgraph_server():
    """Run LangGraph server using Python API"""
    try:
        # Import LangGraph API components
        from langgraph_api import create_app
        from langgraph_api.config import Config
        
        # Create configuration
        config = Config(
            graphs={
                "crawler": "./src/iicrawlermcp/studio/graph_adapter.py:graph"
            },
            dependencies=["./src/iicrawlermcp"],
            store=None,  # Use in-memory store
            auth=None,   # No authentication for development
        )
        
        # Create the FastAPI app
        app = create_app(config)
        
        # Run the server
        print("=" * 60)
        print("Starting LangGraph Studio Server")
        print("=" * 60)
        print("\nAPI: http://127.0.0.1:2024")
        print("Studio UI: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024")
        print("API Docs: http://127.0.0.1:2024/docs")
        print("\nPress Ctrl+C to stop the server")
        print("-" * 60)
        
        # Run with uvicorn
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=2024,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("\nMake sure you have installed the required packages:")
        print("  uv pip install 'langgraph-cli[inmem]'")
        print("  uv pip install langgraph-api")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)


async def run_langgraph_server_async():
    """Async version of the server runner"""
    try:
        from langgraph_runtime_inmem import InMemoryRuntime
        from langgraph_api import create_app
        
        # Create runtime
        runtime = InMemoryRuntime()
        
        # Load graph
        await runtime.load_graphs({
            "crawler": "./studio/graph_adapter.py:graph"
        })
        
        # Create app with runtime
        app = create_app(runtime=runtime)
        
        # Configure server
        config = uvicorn.Config(
            app=app,
            host="127.0.0.1",
            port=2024,
            log_level="info"
        )
        
        server = uvicorn.Server(config)
        
        print("=" * 60)
        print("Starting LangGraph Studio Server (Async)")
        print("=" * 60)
        print("\nAPI: http://127.0.0.1:2024")
        print("Studio UI: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024")
        print("API Docs: http://127.0.0.1:2024/docs")
        print("\nPress Ctrl+C to stop the server")
        print("-" * 60)
        
        await server.serve()
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("\nTrying synchronous version...")
        run_langgraph_server()
    except Exception as e:
        print(f"Error in async server: {e}")
        print("\nTrying synchronous version...")
        run_langgraph_server()


if __name__ == "__main__":
    try:
        # Try async version first
        asyncio.run(run_langgraph_server_async())
    except KeyboardInterrupt:
        print("\n\nShutting down LangGraph Studio...")
        sys.exit(0)
    except:
        # Fallback to sync version
        run_langgraph_server()