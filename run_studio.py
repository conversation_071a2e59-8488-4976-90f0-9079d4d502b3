"""
Direct Python launcher for LangGraph Studio
Run this file directly to start the Studio without using command line
"""

import os
import sys
from pathlib import Path

# Set working directory to project root
project_root = Path(__file__).parent
os.chdir(project_root)

# Add project to path
sys.path.insert(0, str(project_root))

# Set environment variables
os.environ["LANGGRAPH_API_PORT"] = "2024"

# Import and run the LangGraph dev server directly
if __name__ == "__main__":
    try:
        # Import the CLI module
        from langgraph_cli import cli
        
        # Set sys.argv to simulate 'langgraph dev' command with allow-blocking flag
        # This allows synchronous blocking calls in the async environment
        sys.argv = ["langgraph", "dev", "--allow-blocking"]
        
        # Run the CLI
        cli.cli()
    except ImportError:
        print("Error: langgraph-cli is not installed")
        print("Please install it with: uv pip install 'langgraph-cli[inmem]'")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nShutting down LangGraph Studio...")
        sys.exit(0)
    except Exception as e:
        print(f"Error starting LangGraph Studio: {e}")
        sys.exit(1)