"""
Web Search Tools for iICrawlerMCP.

This module provides web search capabilities that can be shared by all agents.
Currently implements Google search via SerpAPI.
"""

import os
import logging
from typing import Optional, Dict, Any
from langchain_core.tools import tool
from serpapi import Client

logger = logging.getLogger(__name__)


@tool
def web_search_serp(query: str, num_results: int = 5) -> str:
    """
    Search the web using Google via SerpAPI.
    
    This tool searches Google and returns organic search results.
    Useful for:
    - Finding domain names for websites
    - Looking up technical terms and documentation
    - Researching best practices
    - Getting current information about topics
    
    Args:
        query: The search query string
        num_results: Maximum number of results to return (default: 5)
        
    Returns:
        Formatted string containing search results with titles, snippets, and links
        
    Example:
        web_search_serp("淘宝官网域名")
        web_search_serp("Python web scraping best practices", num_results=3)
    """
    try:
        # Get API key from environment
        api_key = os.getenv("SERPAPI_API_KEY","75b4dbb04004f9aaca30f779d06bb9637c4ffac65938771d71da9098c06af655")
        if not api_key:
            error_msg = "SERPAPI_API_KEY not found in environment variables"
            logger.error(error_msg)
            return f"❌ {error_msg}"
        
        # Set up search parameters
        params = {
            "engine": "google",
            "q": query,
            "num": num_results  # Limit number of results
        }
        
        logger.info(f"Searching Google for: {query}")
        
        # Perform search using new Client API
        client = Client(api_key=api_key)
        results = client.search(params)
        
        # Check for errors
        if "error" in results:
            error_msg = f"Search API error: {results['error']}"
            logger.error(error_msg)
            return f"❌ {error_msg}"
        
        # Extract organic results
        organic_results = results.get("organic_results", [])
        
        if not organic_results:
            return f"No results found for query: {query}"
        
        # Format results
        formatted_results = []
        formatted_results.append(f"🔍 Search results for: '{query}'")
        formatted_results.append(f"Found {len(organic_results)} results:\n")
        
        for i, result in enumerate(organic_results[:num_results], 1):
            title = result.get("title", "No title")
            snippet = result.get("snippet", "No description available")
            link = result.get("link", "")
            
            formatted_results.append(f"{i}. **{title}**")
            formatted_results.append(f"   {snippet}")
            formatted_results.append(f"   🔗 {link}")
            formatted_results.append("")  # Empty line for spacing
        
        # Add answer box if available
        if "answer_box" in results:
            answer_box = results["answer_box"]
            if "answer" in answer_box:
                formatted_results.insert(2, f"📌 Quick Answer: {answer_box['answer']}\n")
            elif "snippet" in answer_box:
                formatted_results.insert(2, f"📌 Featured Snippet: {answer_box['snippet']}\n")
        
        # Add knowledge graph if available
        if "knowledge_graph" in results:
            kg = results["knowledge_graph"]
            if "description" in kg:
                formatted_results.insert(2, f"📚 Knowledge Graph: {kg.get('title', '')} - {kg['description']}\n")
        
        result_text = "\n".join(formatted_results)
        logger.info(f"Search completed successfully, found {len(organic_results)} results")
        
        return result_text
        
    except ImportError:
        error_msg = "serpapi package not installed. Please install it with: pip install google-search-results"
        logger.error(error_msg)
        return f"❌ {error_msg}"
    except Exception as e:
        error_msg = f"Search failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


# Tool list for easy import
SEARCH_TOOLS = [
    web_search_serp,
]


def get_search_tools():
    """
    Get all search tools.
    
    Returns:
        List of search tools that can be used by agents
    """
    return SEARCH_TOOLS.copy()

if __name__ == '__main__':
    print(web_search_serp("booking的官网是什么"))