"""LangGraph Studio adapter - wraps existing graph without modifications"""
import sys
import os
import asyncio
import logging
from pathlib import Path
from typing import Dict, Any
# Import graph components directly to build without checkpointer
from langgraph.graph import StateGraph, END
from langchain_openai import ChatOpenAI
from iicrawlermcp.core.graph_state import <PERSON><PERSON>ler<PERSON>tate
from iicrawlermcp.core.agent_adapter import get_or_create_agent_node

from iicrawlermcp.core.config import config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Windows 异步环境修复 - 必须在导入 Playwright 之前设置
if sys.platform == 'win32':
    try:
        # 设置 ProactorEventLoop 策略，这是 Playwright 在 Windows 上所需的
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        logger.info("已设置 Windows ProactorEventLoop 策略以支持 Playwright")
    except Exception as e:
        logger.error(f"设置 Windows 事件循环策略失败: {e}")

# 设置环境变量以允许阻塞调用（备用方案）
os.environ["BG_JOB_ISOLATED_LOOPS"] = "true"

# Add src directory to path for package imports
project_root = Path(__file__).parent.parent.parent
src_dir = project_root / "src"
sys.path.insert(0, str(src_dir))

# Set environment variables if not already set
if "OPENAI_API_KEY" not in os.environ:
    # Try to load from .env file
    env_file = project_root / ".env"
    if env_file.exists():
        from dotenv import load_dotenv

        load_dotenv(env_file)


def detect_new_task(state: Dict[str, Any]) -> bool:
    """检测是否有新任务输入"""
    messages = state.get("messages", [])
    if len(messages) < 2:
        return False

    # 检查最后两条HumanMessage是否不同
    human_messages = []
    for msg in messages:
        if hasattr(msg, 'type') and msg.type == 'human':
            human_messages.append(msg.content)

    if len(human_messages) >= 2:
        return human_messages[-1] != human_messages[-2]

    return False


def ensure_complete_state(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    确保状态包含所有必需字段
    
    LangGraph Studio 可能只传入部分状态，需要补充缺失的必需字段
    """
    if isinstance(state, dict):
        # 检测新任务并清理相关状态
        if detect_new_task(state):
            logger.info("检测到新任务，清理相关状态")
            # 清理执行结果但保留消息历史
            state["execution_results"] = []
            state["current_url"] = None
            state["dom_state"] = None
            state["task_plan"] = None
            state["retry_count"] = 0
            state["is_complete"] = False
            state["error"] = None

        # 补充缺失的必需字段，使用 CrawlerState 中定义的默认值
        state.setdefault("execution_results", [])
        state.setdefault("context_variables", {})
        state.setdefault("current_url", None)
        state.setdefault("dom_state", None)
        state.setdefault("task_plan", None)
        state.setdefault("active_agent", "smart_planner")
        state.setdefault("error", None)
        state.setdefault("retry_count", 0)
        state.setdefault("is_complete", False)
        # messages 字段由 add_messages reducer 处理，不需要默认值
    return state


def wrapped_smart_planner_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """包装 smart_planner 节点，确保状态完整初始化并处理用户交互"""
    # 只在第一次交互时（execution_results为空或None）确保状态完整
    # 避免重复初始化导致的流式传输字符重复问题
    if not state.get("execution_results"):
        state = ensure_complete_state(state)

    # 获取原始节点的结果
    result = original_smart_planner_node(state)

    # 检查是否需要用户输入并处理interrupt
    messages = result.get("messages", [])
    if messages:
        last_message = messages[-1]
        if hasattr(last_message, 'content'):
            content = last_message.content

            # 判断是否需要用户输入
            needs_confirmation = False
            needs_clarification = False

            try:
                # 使用项目配置创建LLM进行智能判断
                llm = ChatOpenAI(**config.get_llm_config())

                classification_prompt = f"""
分析以下Smart Planner的输出，判断是否需要用户输入。

Smart Planner输出：
"{content[:1000]}"

请根据以下规则判断：
1. 如果输出中要求用户确认任务理解、确认执行计划，或等待用户同意，返回"WAIT_CONFIRMATION"
2. 如果输出中表示需要更多信息、任务不明确、需要用户澄清或选择，返回"WAIT_CLARIFICATION"  
3. 如果输出中明确表示可以执行、开始操作、任务明确，返回"EXECUTE"

只返回以下三个选项之一：
- WAIT_CONFIRMATION
- WAIT_CLARIFICATION
- EXECUTE
"""

                classification_result = llm.invoke(classification_prompt).content.strip().upper()
                logger.info(f"Smart planner classification: {classification_result}")

                if "WAIT_CONFIRMATION" in classification_result:
                    needs_confirmation = True
                elif "WAIT_CLARIFICATION" in classification_result:
                    needs_clarification = True
                # else: EXECUTE情况，直接继续执行

            except Exception as e:
                logger.error(f"LLM classification failed: {type(e).__name__}: {e}, using fallback logic")

                # 后备方案：简单关键词检测
                if any(word in content for word in ["请确认", "是否", "确认执行"]):
                    needs_confirmation = True
                elif any(word in content for word in ["请提供", "不明确", "请选择"]):
                    needs_clarification = True

            # interrupt调用必须在try-catch外部
            if needs_confirmation:
                from langgraph.types import interrupt
                from langchain_core.messages import HumanMessage
                logger.info("Smart planner需要用户确认，触发interrupt")
                user_response = interrupt("请确认执行计划")
                logger.info(f"收到用户确认: {str(user_response)[:100] if user_response else 'None'}...")

                # 使用用户反馈更新状态并重跑规划，返回最新结果
                updated_state = dict(state)
                msgs = list(state.get("messages", []))
                msgs.append(HumanMessage(content=f"用户回答：{user_response}"))
                updated_state["messages"] = msgs
                context_vars = state.get("context_variables", {}).copy()
                context_vars["user_answers"] = user_response
                context_vars["clarification_received"] = True
                updated_state["context_variables"] = context_vars
                # 仅在第一次交互时补全缺省字段
                if not updated_state.get("execution_results"):
                    updated_state = ensure_complete_state(updated_state)
                return original_smart_planner_node(updated_state)

            elif needs_clarification:
                from langgraph.types import interrupt
                from langchain_core.messages import HumanMessage
                logger.info("Smart planner需要用户澄清，触发interrupt")
                user_response = interrupt("请提供更多信息")
                logger.info(f"收到用户澄清: {str(user_response)[:100] if user_response else 'None'}...")

                # 使用用户澄清更新状态并重跑规划，返回最新结果
                updated_state = dict(state)
                msgs = list(state.get("messages", []))
                msgs.append(HumanMessage(content=f"用户回答：{user_response}"))
                updated_state["messages"] = msgs
                context_vars = state.get("context_variables", {}).copy()
                context_vars["user_answers"] = user_response
                context_vars["clarification_received"] = True
                updated_state["context_variables"] = context_vars
                if not updated_state.get("execution_results"):
                    updated_state = ensure_complete_state(updated_state)
                return original_smart_planner_node(updated_state)

    return result


def wrapped_web_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """包装 web 节点，处理用户介入场景"""
    # 执行原始 web 节点
    result = original_web_node(state)

    # 检查是否需要用户介入
    if needs_user_intervention(state, result):
        from langgraph.types import interrupt
        from langchain_core.messages import HumanMessage

        # 构建用户介入提示
        intervention_message = build_intervention_message(state, result)
        logger.info(f"Web节点需要用户介入: {intervention_message}")

        user_action = interrupt(intervention_message)

        # 根据用户选择更新状态
        if user_action in ["继续", "重试", "同意", "允许", "continue", "retry", "agree", "allow"]:
            logger.info("用户选择继续/重试/同意，清除错误状态并记录授权")
            result["error"] = None
            result["retry_count"] = result.get("retry_count", 0) + 1
            result["messages"].append(HumanMessage(content=f"用户选择: {user_action}"))
            # 记录搜索授权，减少重复征询
            ctx = state.get("context_variables", {}).copy()
            if any(x in str(user_action).lower() for x in ["同意", "允许", "agree", "allow", "continue"]):
                ctx["user_search_consent"] = True
            result["context_variables"] = ctx
        elif user_action in ["停止", "完成", "stop", "finish"]:
            logger.info("用户选择停止，标记任务完成")
            result["is_complete"] = True
            result["messages"].append(HumanMessage(content=f"用户选择: {user_action}"))

    return result


def needs_user_intervention(state: Dict[str, Any], result: Dict[str, Any]) -> bool:
    """判断是否需要用户介入"""
    # 检查错误和重试次数
    if result.get("error") and result.get("retry_count", 0) >= 2:
        return True

    # 检查特定的需要用户介入的情况
    messages = result.get("messages", [])
    if messages:
        last_message = messages[-1]
        if hasattr(last_message, 'content'):
            content = last_message.content.lower()
            intervention_keywords = [
                "需要验证码", "请输入", "需要登录", "需要选择",
                "需要确认", "需要人工", "captcha", "login required",
                # 搜索/授权相关提示，避免循环征求同意
                "请确认", "是否同意", "搜索引擎", "同意使用搜索", "允许使用搜索", "需要授权",
                "search engine", "permission", "authorize", "consent"
            ]
            if any(keyword in content for keyword in intervention_keywords):
                return True

    return False


def build_intervention_message(state: Dict[str, Any], result: Dict[str, Any]) -> str:
    """构建用户介入消息"""
    error = result.get("error")
    retry_count = result.get("retry_count", 0)

    if error:
        return f"执行遇到问题 (重试{retry_count}次): {error}\n请选择: 继续/重试/停止"
    else:
        return "需要用户操作或确认，请选择: 继续/停止"


# 定义web节点后的路由函数
def route_after_web(state: Dict[str, Any]) -> str:
    """web节点后的路由：只返回 continue 或 codegen（interrupt在节点内部处理）"""

    # 优先检查任务完成状态（web节点自我验证的结果）
    if state.get("is_complete", False):
        logger.info("Task completed by web self-validation, offering code generation")
        return "codegen"

    # 获取web节点的最新输出
    execution_results = state.get("execution_results", [])
    web_output = ""

    if execution_results:
        # 找到最后一个web节点的结果
        web_results = [r for r in execution_results if r.get("agent") == "web"]
        if web_results:
            last_web_result = web_results[-1]
            web_output = last_web_result.get("result", {}).get("output", "")

    # 如果没有web输出，获取最新消息
    if not web_output and state.get("messages"):
        last_msg = state["messages"][-1]
        if hasattr(last_msg, 'content'):
            web_output = last_msg.content

    if not web_output:
        logger.info("No web output found, defaulting to continue")
        return "continue"

    try:
        # 使用项目配置创建LLM
        llm = ChatOpenAI(**config.get_llm_config())

        classification_prompt = f"""
分析以下Web Agent的执行输出，判断下一步应该采取什么行动。

Web Agent输出：
"{web_output[:1000]}"

任务状态：
- 是否完成: {state.get('is_complete', False)}
- 错误信息: {state.get('error', 'None')}
- 重试次数: {state.get('retry_count', 0)}

请根据以下规则判断：
1. 如果任务已经成功完成，返回"CODEGEN"（生成代码）
2. 如果操作仍在进行中或需要继续执行，返回"CONTINUE"（继续执行）

注意：用户介入场景已在节点内部通过interrupt处理，此处不需要返回END。

只返回以下两个选项之一：
- CONTINUE
- CODEGEN
"""

        result = llm.invoke(classification_prompt).content.strip().upper()
        logger.info(f"LLM routing decision for web: {result}")

        if "CODEGEN" in result:
            logger.info("Web任务完成，进入代码生成")
            return "codegen"
        else:
            logger.info("Web继续执行")
            return "continue"

    except Exception as e:
        logger.error(f"LLM routing failed: {e}, using fallback logic")

        # 后备方案：简单关键词检测
        if any(word in web_output for word in ["任务完成", "成功", "已完成"]):
            return "codegen"
        else:
            return "continue"


# Create graph without checkpointer for Studio compatibility
workflow = StateGraph(CrawlerState)

# 获取原始节点以供包装函数使用
original_smart_planner_node = get_or_create_agent_node("smart_planner")
original_web_node = get_or_create_agent_node("web")

# Add nodes - wrapped nodes for interrupt handling
# workflow.add_node("smart_planner", wrapped_smart_planner_node)
# workflow.add_node("web", wrapped_web_node)  # Web节点包装版本


workflow.add_node("smart_planner", original_smart_planner_node)
workflow.add_node("web", original_web_node)  # Web节点包装版本
workflow.add_node("codegen", get_or_create_agent_node("codegen"))  # 代码生成节点

# Add edges
workflow.set_entry_point("smart_planner")

# smart_planner直接连接到web（interrupt在节点内部处理）
workflow.add_edge("smart_planner", "web")
workflow.add_edge("web", END)

# # web节点的条件路由（interrupt在节点内部处理）
# workflow.add_conditional_edges(
#     "web",
#     route_after_web,
#     {
#         "continue": "web",      # 继续执行
#         "codegen": "codegen"    # 任务完成，生成代码
#     }
# )
#
# # 代码生成后结束
# workflow.add_edge("codegen", END)

# Compile WITHOUT checkpointer (Studio handles persistence automatically)
graph = workflow.compile()

# Add Studio metadata
graph.name = "iICrawler Web Automation"
graph.description = "Intelligent web crawler with LangGraph workflow for web automation tasks"

# Export the graph for Studio to use
__all__ = ["graph"]
