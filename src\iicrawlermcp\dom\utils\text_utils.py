"""
DOM text processing utilities.

This module provides utilities for processing and analyzing text content from DOM elements.
"""

import re
from typing import List, Dict, Any, Optional
from ..models.base_models import ElementInfo


class TextUtils:
    """文本处理工具"""

    @staticmethod
    def normalize_text(text: str) -> str:
        """
        标准化文本

        Args:
            text: 原始文本

        Returns:
            标准化后的文本
        """
        if not text:
            return ""

        # 移除多余的空白字符
        normalized = re.sub(r"\s+", " ", text.strip())

        # 移除特殊字符（保留基本标点）
        normalized = re.sub(r'[^\w\s\u4e00-\u9fff.,!?;:()[\]{}"\'-]', "", normalized)

        return normalized

    @staticmethod
    def extract_meaningful_text(element: ElementInfo) -> str:
        """
        提取有意义的文本

        Args:
            element: 元素信息

        Returns:
            有意义的文本内容
        """
        text = element.text_content

        if not text or not text.strip():
            # 如果没有文本内容，尝试从属性中获取
            for attr in ["alt", "title", "placeholder", "value"]:
                attr_value = element.attributes.get(attr, "").strip()
                if attr_value:
                    return TextUtils.normalize_text(attr_value)
            return ""

        # 标准化文本
        meaningful_text = TextUtils.normalize_text(text)

        # 过滤掉过短或无意义的文本
        if len(meaningful_text) < 2:
            return ""

        # 过滤掉纯数字或纯符号
        if re.match(r"^[\d\s\W]+$", meaningful_text):
            return ""

        return meaningful_text

    @staticmethod
    def is_meaningful_text(text: str, min_length: int = 2) -> bool:
        """
        判断文本是否有意义

        Args:
            text: 文本内容
            min_length: 最小长度

        Returns:
            是否有意义
        """
        if not text or len(text.strip()) < min_length:
            return False

        normalized = TextUtils.normalize_text(text)

        # 检查是否包含字母或中文字符
        if not re.search(r"[a-zA-Z\u4e00-\u9fff]", normalized):
            return False

        return True

    @staticmethod
    def extract_keywords(text: str, max_keywords: int = 5) -> List[str]:
        """
        提取关键词

        Args:
            text: 文本内容
            max_keywords: 最大关键词数量

        Returns:
            关键词列表
        """
        if not text:
            return []

        # 标准化文本
        normalized = TextUtils.normalize_text(text.lower())

        # 分词（简单的空格分词）
        words = normalized.split()

        # 过滤停用词和短词
        stop_words = {
            "the",
            "a",
            "an",
            "and",
            "or",
            "but",
            "in",
            "on",
            "at",
            "to",
            "for",
            "of",
            "with",
            "by",
            "is",
            "are",
            "was",
            "were",
            "be",
            "been",
            "have",
            "has",
            "had",
            "do",
            "does",
            "did",
            "will",
            "would",
            "could",
            "should",
        }

        keywords = []
        for word in words:
            if len(word) > 2 and word not in stop_words:
                keywords.append(word)

        # 去重并限制数量
        unique_keywords = list(dict.fromkeys(keywords))  # 保持顺序的去重
        return unique_keywords[:max_keywords]

    @staticmethod
    def calculate_text_similarity(text1: str, text2: str) -> float:
        """
        计算文本相似度（简单的词汇重叠度）

        Args:
            text1: 第一个文本
            text2: 第二个文本

        Returns:
            相似度分数 (0-1)
        """
        if not text1 or not text2:
            return 0.0

        # 提取关键词
        keywords1 = set(TextUtils.extract_keywords(text1))
        keywords2 = set(TextUtils.extract_keywords(text2))

        if not keywords1 or not keywords2:
            return 0.0

        # 计算交集和并集
        intersection = keywords1.intersection(keywords2)
        union = keywords1.union(keywords2)

        # 计算Jaccard相似度
        return len(intersection) / len(union) if union else 0.0

    @staticmethod
    def find_similar_elements(
        target_element: ElementInfo,
        candidate_elements: List[ElementInfo],
        similarity_threshold: float = 0.3,
    ) -> List[ElementInfo]:
        """
        查找相似的元素

        Args:
            target_element: 目标元素
            candidate_elements: 候选元素列表
            similarity_threshold: 相似度阈值

        Returns:
            相似元素列表
        """
        target_text = TextUtils.extract_meaningful_text(target_element)
        if not target_text:
            return []

        similar_elements = []
        for candidate in candidate_elements:
            if candidate.id == target_element.id:
                continue

            candidate_text = TextUtils.extract_meaningful_text(candidate)
            if not candidate_text:
                continue

            similarity = TextUtils.calculate_text_similarity(
                target_text, candidate_text
            )
            if similarity >= similarity_threshold:
                similar_elements.append(candidate)

        # 按相似度排序
        similar_elements.sort(
            key=lambda x: TextUtils.calculate_text_similarity(
                target_text, TextUtils.extract_meaningful_text(x)
            ),
            reverse=True,
        )

        return similar_elements

    @staticmethod
    def classify_text_type(text: str) -> str:
        """
        分类文本类型

        Args:
            text: 文本内容

        Returns:
            文本类型 ('email', 'url', 'phone', 'number', 'text')
        """
        if not text:
            return "empty"

        text = text.strip()

        # 邮箱
        if re.match(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", text):
            return "email"

        # URL
        if re.match(r"^https?://[^\s]+$", text):
            return "url"

        # 电话号码（简单匹配）
        if re.match(r"^[\d\s\-\+\(\)]{7,}$", text):
            return "phone"

        # 纯数字
        if re.match(r"^\d+(\.\d+)?$", text):
            return "number"

        # 默认为文本
        return "text"

    @staticmethod
    def truncate_text(text: str, max_length: int = 50, suffix: str = "...") -> str:
        """
        截断文本

        Args:
            text: 原始文本
            max_length: 最大长度
            suffix: 后缀

        Returns:
            截断后的文本
        """
        if not text or len(text) <= max_length:
            return text

        return text[: max_length - len(suffix)] + suffix

    @staticmethod
    def clean_whitespace(text: str) -> str:
        """
        清理空白字符

        Args:
            text: 原始文本

        Returns:
            清理后的文本
        """
        if not text:
            return ""

        # 替换各种空白字符为单个空格
        cleaned = re.sub(r"[\s\u00A0\u2000-\u200B\u2028\u2029]+", " ", text)

        return cleaned.strip()

    @staticmethod
    def extract_numbers(text: str) -> List[float]:
        """
        从文本中提取数字

        Args:
            text: 文本内容

        Returns:
            数字列表
        """
        if not text:
            return []

        # 匹配整数和小数
        number_pattern = r"-?\d+(?:\.\d+)?"
        matches = re.findall(number_pattern, text)

        numbers = []
        for match in matches:
            try:
                numbers.append(float(match))
            except ValueError:
                continue

        return numbers

    @staticmethod
    def get_text_statistics(elements: List[ElementInfo]) -> Dict[str, Any]:
        """
        获取文本统计信息

        Args:
            elements: 元素列表

        Returns:
            文本统计信息
        """
        if not elements:
            return {
                "total_elements": 0,
                "elements_with_text": 0,
                "total_characters": 0,
                "average_text_length": 0,
                "text_types": {},
            }

        elements_with_text = 0
        total_characters = 0
        text_types = {}

        for element in elements:
            meaningful_text = TextUtils.extract_meaningful_text(element)
            if meaningful_text:
                elements_with_text += 1
                total_characters += len(meaningful_text)

                text_type = TextUtils.classify_text_type(meaningful_text)
                text_types[text_type] = text_types.get(text_type, 0) + 1

        average_length = (
            total_characters / elements_with_text if elements_with_text > 0 else 0
        )

        return {
            "total_elements": len(elements),
            "elements_with_text": elements_with_text,
            "total_characters": total_characters,
            "average_text_length": round(average_length, 2),
            "text_types": text_types,
        }
