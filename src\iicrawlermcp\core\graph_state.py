"""
LangGraph状态管理模块

定义统一的爬虫状态，用于在Agent间共享上下文和执行历史。
这是迁移到LangGraph架构的核心组件。
"""

from typing import List, Dict, Any, Optional, Annotated
from langgraph.graph import MessagesState
import operator


class CrawlerState(MessagesState):
    """
    统一的爬虫状态定义

    继承自MessagesState以利用LangGraph内置的消息去重和流式传输优化。
    这个状态在整个爬虫流程中共享，自动管理Agent间的上下文传递。
    解决了原有架构中每次创建新Agent实例导致的上下文丢失问题。
    """
    # messages字段由MessagesState提供，自动处理去重和流式传输

    # 当前页面URL
    current_url: Optional[str]

    # DOM状态缓存（避免重复提取）
    dom_state: Optional[Dict[str, Any]]

    # 任务执行计划
    task_plan: Optional[Dict[str, Any]]

    # 当前活跃的Agent名称
    active_agent: str

    # 执行结果累积（使用operator.add自动累积）
    execution_results: Annotated[List[Dict[str, Any]], operator.add]

    # 共享上下文变量（用于Agent间传递特定信息）
    context_variables: Dict[str, Any]

    # 错误信息
    error: Optional[str]

    # 重试次数
    retry_count: int

    # 任务完成状态
    is_complete: Annotated[bool, lambda x, y: y if y is not None else x]


def create_initial_state(task_description: str) -> CrawlerState:
    """
    创建初始状态

    Args:
        task_description: 用户的任务描述

    Returns:
        初始化的爬虫状态
    """
    from langchain_core.messages import HumanMessage

    return {
        "messages": [HumanMessage(content=task_description)],
        "current_url": None,
        "dom_state": None,
        "task_plan": None,
        "active_agent": "planner",  # 默认从规划器开始
        "execution_results": [],
        "context_variables": {},
        "error": None,
        "retry_count": 0,
        "is_complete": False,
    }