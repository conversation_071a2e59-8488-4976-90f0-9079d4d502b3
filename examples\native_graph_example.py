"""
Example usage of the native LangGraph implementation.

This example demonstrates how to use the new native nodes without
the overhead of LangChain Agent + AgentNodeAdapter architecture.
"""

import asyncio
import logging
from pathlib import Path
import sys

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from iicrawlermcp import build_native_crawler, NativeCrawler
from iicrawlermcp.nodes import smart_planner_node, web_node, codegen_node
from iicrawlermcp.core.graph_state import create_initial_state

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_native_crawler():
    """Example using the native crawler class."""
    print("=== Native Crawler Example ===")
    
    # Create native crawler
    crawler = build_native_crawler()
    
    # Execute a task
    task = "打开 google.com，搜索 'Python LangGraph'，点击第一个结果"
    
    try:
        result = crawler.invoke(task)
        
        print(f"Task completed!")
        print(f"Messages: {len(result.get('messages', []))}")
        print(f"Execution results: {len(result.get('execution_results', []))}")
        print(f"Is complete: {result.get('is_complete', False)}")
        
        # Print last message
        messages = result.get('messages', [])
        if messages:
            last_message = messages[-1]
            print(f"Last message: {last_message.content[:200]}...")
            
    except Exception as e:
        print(f"Error: {e}")


def example_streaming_crawler():
    """Example using streaming execution."""
    print("\n=== Streaming Crawler Example ===")
    
    crawler = build_native_crawler()
    task = "导航到 booking.com，搜索上海的酒店"
    
    try:
        for i, update in enumerate(crawler.stream(task)):
            print(f"Update {i+1}: {list(update.keys())}")
            
            # Print messages from each update
            for node_name, node_state in update.items():
                if isinstance(node_state, dict) and 'messages' in node_state:
                    messages = node_state['messages']
                    if messages:
                        last_msg = messages[-1]
                        print(f"  {node_name}: {last_msg.content[:100]}...")
                        
    except Exception as e:
        print(f"Streaming error: {e}")


def example_individual_nodes():
    """Example using individual nodes directly."""
    print("\n=== Individual Nodes Example ===")
    
    task = "分析 github.com 网站并截图"
    
    # Create initial state
    state = create_initial_state(task)
    
    try:
        # Step 1: Smart planning
        print("1. Running smart planner...")
        planner_result = smart_planner_node(state)
        
        # Update state with planner results
        state.update(planner_result)
        print(f"   Plan generated: {len(planner_result.get('messages', []))} messages")
        
        # Step 2: Web execution
        print("2. Running web automation...")
        web_result = web_node(state)
        
        # Update state with web results
        state.update(web_result)
        print(f"   Web execution: {web_result.get('is_complete', False)}")
        
        # Step 3: Code generation (if task is complete)
        if web_result.get('is_complete', False):
            print("3. Running code generation...")
            codegen_result = codegen_node(state)
            
            print(f"   Code generation completed")
            if codegen_result.get('context_variables', {}).get('code_generated'):
                print("   Code file saved!")
        else:
            print("3. Task not complete, skipping code generation")
            
    except Exception as e:
        print(f"Individual nodes error: {e}")


def example_quick_functions():
    """Example using quick convenience functions."""
    print("\n=== Quick Functions Example ===")
    
    try:
        from iicrawlermcp import quick_plan_native, quick_web_native
        
        task = "获取 python.org 首页的标题"
        
        # Quick planning
        print("1. Quick planning...")
        plan_result = quick_plan_native(task)
        print(f"   Plan: {len(plan_result.get('messages', []))} messages")
        
        # Quick web execution
        print("2. Quick web execution...")
        web_result = quick_web_native(task)
        print(f"   Web result: {web_result.get('is_complete', False)}")
        
    except Exception as e:
        print(f"Quick functions error: {e}")


def compare_performance():
    """Compare performance between native and legacy implementations."""
    print("\n=== Performance Comparison ===")
    
    import time
    
    task = "导航到 example.com 并截图"
    
    # Test native implementation
    print("Testing native implementation...")
    start_time = time.time()
    try:
        crawler = build_native_crawler()
        result = crawler.invoke(task)
        native_time = time.time() - start_time
        print(f"   Native time: {native_time:.2f}s")
    except Exception as e:
        print(f"   Native error: {e}")
        native_time = None
    
    # Test legacy implementation
    print("Testing legacy implementation...")
    start_time = time.time()
    try:
        from iicrawlermcp.studio.graph_adapter import graph
        from iicrawlermcp.core.graph_state import create_initial_state
        
        initial_state = create_initial_state(task)
        result = graph.invoke(initial_state)
        legacy_time = time.time() - start_time
        print(f"   Legacy time: {legacy_time:.2f}s")
    except Exception as e:
        print(f"   Legacy error: {e}")
        legacy_time = None
    
    # Compare
    if native_time and legacy_time:
        improvement = ((legacy_time - native_time) / legacy_time) * 100
        print(f"   Performance improvement: {improvement:.1f}%")


def main():
    """Run all examples."""
    print("Native LangGraph Implementation Examples")
    print("=" * 50)
    
    # Run examples
    example_native_crawler()
    example_streaming_crawler()
    example_individual_nodes()
    example_quick_functions()
    compare_performance()
    
    print("\n" + "=" * 50)
    print("Examples completed!")
    print("\nKey benefits of native implementation:")
    print("- No LangChain Agent overhead")
    print("- Direct tool calling")
    print("- Simplified state management")
    print("- Better performance")
    print("- Easier debugging")


if __name__ == "__main__":
    main()
