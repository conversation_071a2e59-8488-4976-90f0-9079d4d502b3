"""
DOM extraction and analysis module.

This module provides DOM element extraction, analysis, and formatting capabilities
using Playwright browser automation and JavaScript DOM manipulation.
"""

# Core functionality
from .core.extractor import DOMExtractor
from .core.cache_manager import CacheManager

# Data models
from .models.base_models import ElementInfo
from .models.element_models import (
    DOMElementOutput,
    LinkElement,
    ButtonElement,
    InputElement,
    ActionableElement,
)
from .models.output_models import (
    NavigationElement,
    FormElement,
    MediaElement,
    ExtractionResult,
)

# Formatters
from .formatters.element_formatters import DOMFormatter
from .formatters.base_formatter import BaseFormatter

# Utilities
from .utils.element_filters import ElementFilter
from .utils.text_utils import TextUtils

__all__ = [
    # Core
    "DOMExtractor",
    "CacheManager",
    # Models
    "ElementInfo",
    "DOMElementOutput",
    "LinkElement",
    "ButtonElement",
    "InputElement",
    "ActionableElement",
    "NavigationElement",
    "FormElement",
    "MediaElement",
    "ExtractionResult",
    # Formatters
    "DOMFormatter",
    "BaseFormatter",
    # Utilities
    "ElementFilter",
    "TextUtils",
]
