"""
LangGraph Studio integration package for iICrawlerMCP.

This package provides LangGraph Studio integration for the iICrawlerMCP project,
allowing visual debugging and monitoring of web automation workflows.

Components:
- graph_adapter: Main adapter that wraps the existing graph for Studio compatibility
- studio_launcher: Convenience launcher script for starting Studio
"""

from .graph_adapter import graph

__all__ = ["graph"]
