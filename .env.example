# iICrawlerMCP Configuration Example
# Copy this file to .env and modify the values as needed

# OpenAI/LLM Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# LangSmith Configuration (optional)
LANGSMITH_API_KEY=your_langsmith_api_key_here

# Browser Configuration
HEADLESS=false
BROWSER_TIMEOUT=60000

# Path Configuration
DEFAULT_SCREENSHOT_PATH=screenshots/screenshot.png
VERBOSE=true

# DOM Highlight Configuration
# Enable/disable element highlighting during DOM analysis
DOM_HIGHLIGHT_ENABLED=true

# Visual appearance settings
DOM_HIGHLIGHT_BORDER_WIDTH=2
DOM_HIGHLIGHT_OPACITY=0.1
DOM_HIGHLIGHT_Z_INDEX=2147483647

# Label settings
DOM_HIGHLIGHT_LABEL_FONT_SIZE_MIN=8
DOM_HIGHLIGHT_LABEL_FONT_SIZE_MAX=12
DOM_HIGHLIGHT_LABEL_PADDING=1px 4px
DOM_HIGHLIGHT_LABEL_BORDER_RADIUS=4px

# Highlight colors (comma-separated hex colors)
# These colors will be used in rotation for highlighting different elements
DOM_HIGHLIGHT_COLORS=#FF6B6B,#4ECDC4,#45B7D1,#96CEB4,#FFEAA7,#DDA0DD,#98D8C8,#F7DC6F,#BB8FCE,#85C1E9,#F8C471,#82E0AA,#F1948A,#85C1E9,#D7BDE2,#A3E4D7,#DC143C,#4682B4

# DOM Extraction Configuration
# Focus highlight index (-1 = highlight all elements, >=0 = highlight only specific element)
DOM_FOCUS_HIGHLIGHT_INDEX=-1

# Viewport expansion in pixels (0 = exact viewport, >0 = expanded viewport, -1 = all elements)
DOM_VIEWPORT_EXPANSION=0

# Debug mode (true = enable console logging, false = disable)
DOM_DEBUG_MODE=false

# Color Scheme Examples:
# Pastel colors: #FFB3BA,#FFDFBA,#FFFFBA,#BAFFC9,#BAE1FF,#E1BAFF,#FFBAE1
# Bright colors: #FF0000,#00FF00,#0000FF,#FFFF00,#FF00FF,#00FFFF,#FFA500
# Material colors: #F44336,#E91E63,#9C27B0,#673AB7,#3F51B5,#2196F3,#03A9F4,#00BCD4,#009688,#4CAF50,#8BC34A,#CDDC39,#FFEB3B,#FFC107,#FF9800,#FF5722
