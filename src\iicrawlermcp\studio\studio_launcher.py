"""Convenience launcher for LangGraph Studio"""
import os
import subprocess
import sys
import platform
from pathlib import Path

def check_docker():
    """Check if Docker is running"""
    try:
        result = subprocess.run(
            ["docker", "info"],
            capture_output=True,
            text=True,
            check=True
        )
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def install_langgraph_cli():
    """Install langgraph-cli if not present"""
    try:
        subprocess.run(
            ["langgraph", "--version"],
            check=True,
            capture_output=True
        )
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Installing langgraph-cli...")
        try:
            # Try uv first
            subprocess.run(
                ["uv", "pip", "install", "langgraph-cli"],
                check=True
            )
            print("langgraph-cli installed successfully with uv!")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            # Fallback to pip if uv is not available
            try:
                subprocess.run(
                    [sys.executable, "-m", "pip", "install", "langgraph-cli"],
                    check=True
                )
                print("langgraph-cli installed successfully with pip!")
                return True
            except subprocess.CalledProcessError as e:
                print(f"Failed to install langgraph-cli: {e}")
                return False

def launch_studio():
    """Launch LangGraph Studio with proper configuration"""
    
    # Ensure we're in the project root directory
    project_root = Path(__file__).parent.parent.parent
    os.chdir(project_root)
    
    print("=" * 60)
    print("LangGraph Studio Launcher for iICrawlerMCP")
    print("=" * 60)
    
    # Note: Docker check removed as it's not required with inmem mode
    print("Starting in-memory mode (no Docker required)...")
    
    # Check and install langgraph CLI
    if not install_langgraph_cli():
        print("\nError: Failed to install langgraph-cli")
        print("Please install it manually with: uv pip install 'langgraph-cli[inmem]'")
        return 1
    
    # Display configuration info
    print("\nConfiguration:")
    print(f"  Project Root: {project_root}")
    print(f"  Config File: langgraph.json")
    print(f"  Graph Module: src/iicrawlermcp/studio/graph_adapter.py")
    print(f"  Environment: src/iicrawlermcp/studio/.env.studio")
    
    # Launch Studio
    print("\n" + "=" * 60)
    print("Starting LangGraph Studio...")
    print("=" * 60)
    print("\nStudio will be available at:")
    print("https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024")
    print("\nPress Ctrl+C to stop the server")
    print("-" * 60 + "\n")
    
    try:
        # Run langgraph dev command
        result = subprocess.run(["langgraph", "dev"])
        return result.returncode
    except KeyboardInterrupt:
        print("\n\nShutting down LangGraph Studio...")
        return 0
    except Exception as e:
        print(f"\nError launching Studio: {e}")
        return 1

def main():
    """Main entry point"""
    # Check Python version
    if sys.version_info < (3, 9):
        print("Error: Python 3.9 or higher is required")
        sys.exit(1)
    
    # Check operating system
    os_name = platform.system()
    if os_name == "Windows":
        print("Running on Windows...")
    elif os_name == "Darwin":
        print("Running on macOS...")
    elif os_name == "Linux":
        print("Running on Linux...")
    else:
        print(f"Warning: Untested operating system: {os_name}")
    
    # Launch Studio
    exit_code = launch_studio()
    sys.exit(exit_code)

if __name__ == "__main__":
    main()