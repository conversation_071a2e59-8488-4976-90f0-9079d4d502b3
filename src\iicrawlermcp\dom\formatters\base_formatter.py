"""
Base DOM formatter.

This module contains the base formatter class for DOM elements.
"""

from typing import List, Any, Dict
from ..models.base_models import ElementInfo


class BaseFormatter:
    """基础格式化器"""

    @staticmethod
    def format_element_list(elements: List[ElementInfo], element_type: str) -> str:
        """
        通用的元素列表格式化

        Args:
            elements: 元素列表
            element_type: 元素类型描述

        Returns:
            格式化后的字符串
        """
        if not elements:
            return f"未找到{element_type}元素"

        lines = [f"找到 {len(elements)} 个{element_type}元素:"]
        lines.append("")

        for i, element in enumerate(elements, 1):
            lines.append(f"{i}. {BaseFormatter._format_single_element(element)}")

        return "\n".join(lines)

    @staticmethod
    def _format_single_element(element: ElementInfo) -> str:
        """
        格式化单个元素

        Args:
            element: 元素信息

        Returns:
            格式化后的字符串
        """
        parts = []

        # 基本信息
        if element.text_content:
            text = (
                element.text_content[:50] + "..."
                if len(element.text_content) > 50
                else element.text_content
            )
            parts.append(f"文本: '{text}'")

        # 标签和属性
        tag_info = element.tag_name
        if element.attributes.get("id"):
            tag_info += f"#{element.attributes['id']}"
        if element.attributes.get("class"):
            classes = element.attributes["class"].split()[:2]  # 只显示前2个类
            tag_info += f".{'.'.join(classes)}"

        parts.append(f"标签: {tag_info}")

        # 状态
        status_parts = []
        if element.is_visible:
            status_parts.append("可见")
        if element.is_interactive:
            status_parts.append("可交互")
        if element.is_in_viewport:
            status_parts.append("视口内")

        if status_parts:
            parts.append(f"状态: {' | '.join(status_parts)}")

        # XPath
        parts.append(f"xpath: {element.xpath}")

        return " | ".join(parts)

    @staticmethod
    def format_summary(elements: List[ElementInfo], element_type: str) -> str:
        """
        格式化元素摘要信息

        Args:
            elements: 元素列表
            element_type: 元素类型描述

        Returns:
            摘要信息字符串
        """
        if not elements:
            return f"未找到{element_type}元素"

        total = len(elements)
        visible = sum(1 for elem in elements if elem.is_visible)
        interactive = sum(1 for elem in elements if elem.is_interactive)
        in_viewport = sum(1 for elem in elements if elem.is_in_viewport)

        summary_parts = [
            f"总计: {total}个",
            f"可见: {visible}个",
            f"可交互: {interactive}个",
            f"视口内: {in_viewport}个",
        ]

        return f"{element_type}元素统计 - {' | '.join(summary_parts)}"

    @staticmethod
    def format_compact_list(elements: List[ElementInfo], max_items: int = 10) -> str:
        """
        紧凑格式的元素列表

        Args:
            elements: 元素列表
            max_items: 最大显示项目数

        Returns:
            紧凑格式的字符串
        """
        if not elements:
            return "无元素"

        lines = []
        display_elements = elements[:max_items]

        for i, element in enumerate(display_elements, 1):
            # 简化的单行格式
            text = (
                element.text_content[:30] + "..."
                if len(element.text_content) > 30
                else element.text_content
            )
            highlight = (
                f"[{element.highlight_index}]"
                if element.highlight_index is not None
                else ""
            )

            line = f"{i}. {highlight} {element.tag_name}"
            if text:
                line += f": '{text}'"

            lines.append(line)

        if len(elements) > max_items:
            lines.append(f"... 还有 {len(elements) - max_items} 个元素")

        return "\n".join(lines)

    @staticmethod
    def format_table_style(
        elements: List[ElementInfo], columns: List[str] = None
    ) -> str:
        """
        表格样式的元素格式化

        Args:
            elements: 元素列表
            columns: 要显示的列名

        Returns:
            表格格式的字符串
        """
        if not elements:
            return "无元素数据"

        if columns is None:
            columns = ["序号", "类型", "文本", "状态", "XPath"]

        # 计算列宽
        col_widths = {col: len(col) for col in columns}

        # 准备数据行
        rows = []
        for i, element in enumerate(elements, 1):
            row = {}
            row["序号"] = str(i)
            row["类型"] = element.tag_name
            row["文本"] = (
                element.text_content[:20] + "..."
                if len(element.text_content) > 20
                else element.text_content
            )

            status_parts = []
            if element.is_visible:
                status_parts.append("可见")
            if element.is_interactive:
                status_parts.append("交互")
            row["状态"] = "|".join(status_parts) if status_parts else "无"

            row["XPath"] = (
                element.xpath[:30] + "..." if len(element.xpath) > 30 else element.xpath
            )

            rows.append(row)

            # 更新列宽
            for col in columns:
                if col in row:
                    col_widths[col] = max(col_widths[col], len(str(row[col])))

        # 构建表格
        lines = []

        # 表头
        header = " | ".join(col.ljust(col_widths[col]) for col in columns)
        lines.append(header)
        lines.append("-" * len(header))

        # 数据行
        for row in rows:
            line = " | ".join(
                str(row.get(col, "")).ljust(col_widths[col]) for col in columns
            )
            lines.append(line)

        return "\n".join(lines)
