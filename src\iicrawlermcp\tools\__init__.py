"""
Tools module for iICrawlerMCP.

This module contains <PERSON><PERSON><PERSON><PERSON> tools for browser automation and web interaction.
"""

from .browser_tools import (
    get_tools,
    get_browser_specific_tools,
    cleanup_tools,
    close_browser,
    # Core tools used by WebAgent
    navigate_browser,
    take_screenshot,
    get_page_info,
    click_element,
    type_text,
    scroll_page,
    scroll_to_element,
    select_option,
)

# dom_tools removed - use dom_tools_enhanced instead
from .dom_tools import (
    dom_get_buttons_enhanced,
    dom_get_inputs_enhanced,
    dom_get_links_enhanced,
    dom_get_form_elements,
    dom_get_navigation_elements,
    dom_get_media_elements,
    dom_get_content_elements,
    dom_get_clickable_elements,
    dom_get_focusable_elements,
    dom_get_interactive_elements_smart,
    get_enhanced_dom_tools,
    ENHANCED_DOM_TOOLS,
)
from .prompt_tools import (
    generate_standard_template,
    search_web_context,
    generate_clarifying_questions,
    generate_optimized_prompt,
    get_prompt_optimization_tools,
    PROMPT_OPTIMIZATION_TOOLS,
)
from .search_tools import (
    web_search_serp,
    get_search_tools,
    SEARCH_TOOLS,
)
from .codegen_tools import (
    extract_and_generate_code,
    yolo_validate_and_fix,
    save_generated_code,
    get_codegen_tools,
    CODEGEN_TOOLS,
)
from .smart_planner_tools import (
    understand_and_analyze,
    generate_smart_plan,
    get_current_datetime_info,
    get_smart_planner_tools,
    SMART_PLANNER_TOOLS,
    get_tool_info,
)

__all__ = [
    # Browser tools
    "get_tools",
    "get_browser_specific_tools",
    "cleanup_tools",
    "close_browser",
    # Core browser tools used by WebAgent
    "navigate_browser",
    "take_screenshot",
    "get_page_info",
    "click_element",
    "type_text",
    "scroll_page",
    "scroll_to_element",
    "select_option",
    # Enhanced DOM tools
    "dom_get_buttons_enhanced",
    "dom_get_inputs_enhanced",
    "dom_get_links_enhanced",
    "dom_get_form_elements",
    "dom_get_navigation_elements",
    "dom_get_media_elements",
    "dom_get_content_elements",
    "dom_get_clickable_elements",
    "dom_get_focusable_elements",
    "dom_get_interactive_elements_smart",
    "get_enhanced_dom_tools",
    "ENHANCED_DOM_TOOLS",
    # Prompt optimization tools (4 core LLM-driven tools)
    "generate_standard_template",
    "search_web_context",
    "generate_clarifying_questions",
    "generate_optimized_prompt",
    "get_prompt_optimization_tools",
    "PROMPT_OPTIMIZATION_TOOLS",
    # Search tools
    "web_search_serp",
    "get_search_tools",
    "SEARCH_TOOLS",
    # Code generation tools (3 specialized tools)
    "extract_and_generate_code",
    "yolo_validate_and_fix", 
    "save_generated_code",
    "get_codegen_tools",
    "CODEGEN_TOOLS",
    # Smart planner tools (4 unified tools)
    "understand_and_analyze",
    "generate_smart_plan",
    "get_current_datetime_info",
    "get_smart_planner_tools",
    "SMART_PLANNER_TOOLS",
    "get_tool_info",
]
