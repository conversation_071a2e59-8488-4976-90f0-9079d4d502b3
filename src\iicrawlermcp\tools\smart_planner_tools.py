"""
Smart Planner Tools for iICrawlerMCP.

This module provides unified tools that combine task planning and prompt optimization
capabilities. The SmartPlannerAgent uses these tools to understand user intent,
analyze complexity, and generate optimized execution plans.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from langchain_core.tools import tool, BaseTool

logger = logging.getLogger(__name__)


@tool
def understand_and_analyze(user_input: str) -> str:
    """
    统一的理解和分析工具。
    
    功能整合：
    - 意图理解和解析
    - 任务复杂度分析
    - 识别模糊点和需要澄清的地方
    - 评估执行难度和所需资源
    
    Args:
        user_input: 用户的原始输入
        
    Returns:
        结构化的分析结果JSON，包含：
        - intent: 用户意图
        - complexity: 任务复杂度(simple/medium/complex)
        - clarity_score: 清晰度评分(0-10)
        - ambiguous_points: 模糊点列表
        - key_requirements: 关键需求
        - suggested_approach: 建议的执行方式
        
    Example:
        understand_and_analyze("爬取淘宝商品数据")
    """
    try:
        from langchain_openai import ChatOpenAI
        from ..core.config import config
        
        logger.info(f"Starting analysis for user input: {user_input[:100]}...")
        
        llm = ChatOpenAI(**config.get_llm_config())
        
        analysis_prompt = f"""
作为智能任务分析专家，请深度分析以下用户输入。

用户输入："{user_input}"

请从以下维度进行全面分析：

1. **意图识别**：用户的真实目标是什么？
2. **复杂度评估**：任务的技术难度（simple/medium/complex）
3. **清晰度评分**：指令的明确程度（0-10分，10分最清晰）
4. **模糊点识别**：哪些信息不够明确，需要澄清？
5. **关键需求**：完成任务必须满足的条件
6. **执行方式**：建议的技术路径和方法

重要：你必须且只能输出JSON格式，不要包含任何其他文本、解释或markdown标记。

JSON输出模板：
{{
  "intent": "用户的核心意图描述",
  "task_category": "爬虫/自动化/数据提取/其他",
  "complexity": "simple|medium|complex",
  "clarity_score": 7,
  "ambiguous_points": [
    "缺失的具体信息1",
    "需要澄清的要点2"
  ],
  "key_requirements": [
    "关键需求1",
    "关键需求2"
  ],
  "suggested_approach": "建议的执行策略和技术路径",
  "estimated_time": "预估执行时间",
  "required_tools": ["tool1", "tool2"],
  "potential_challenges": ["挑战1", "挑战2"],
  "success_criteria": "成功完成的标准"
}}

现在输出分析结果JSON：
{{"""
        
        # 添加预填充开始来强制JSON输出
        response = llm.invoke(analysis_prompt)
        analysis_json = response.content.strip()
        
        # 确保JSON格式正确
        if not analysis_json.startswith("{"):
            analysis_json = "{" + analysis_json
            
        # 验证JSON格式
        try:
            analysis_result = json.loads(analysis_json)
            logger.info(f"Analysis completed successfully. Complexity: {analysis_result.get('complexity', 'unknown')}")
            return analysis_json
        except json.JSONDecodeError as e:
            logger.warning(f"JSON decode error: {e}")
            # JSON修复策略
            import re
            
            # 提取大括号内容
            brace_match = re.search(r"\{[\s\S]*\}", analysis_json)
            if brace_match:
                try:
                    fixed_json = brace_match.group()
                    json.loads(fixed_json)  # 验证
                    return fixed_json
                except:
                    pass
            
            # 返回默认分析结果
            default_analysis = {
                "intent": user_input,
                "task_category": "未分类",
                "complexity": "medium",
                "clarity_score": 5,
                "ambiguous_points": ["需要更详细的信息"],
                "key_requirements": ["理解用户意图"],
                "suggested_approach": "需要进一步分析",
                "estimated_time": "未知",
                "required_tools": ["browser", "dom"],
                "potential_challenges": ["信息不够明确"],
                "success_criteria": "达成用户目标"
            }
            return json.dumps(default_analysis, ensure_ascii=False)
            
    except Exception as e:
        logger.error(f"Failed to analyze user input: {e}")
        error_result = {
            "error": str(e),
            "intent": user_input,
            "complexity": "unknown",
            "clarity_score": 0,
            "ambiguous_points": [f"分析失败: {str(e)}"],
            "key_requirements": [],
            "suggested_approach": "需要重新分析",
            "estimated_time": "未知",
            "required_tools": [],
            "potential_challenges": ["系统错误"],
            "success_criteria": "解决错误后重试"
        }
        return json.dumps(error_result, ensure_ascii=False)


@tool
def generate_smart_plan(analysis_result: str, additional_context: str = "") -> str:
    """
    智能计划生成工具。
    
    功能整合：
    - 生成优化后的任务描述
    - 创建详细的执行步骤
    - 包含澄清问题（如果需要）
    - 提供执行建议和注意事项
    
    Args:
        analysis_result: understand_and_analyze的输出
        additional_context: 额外上下文（如用户回答）
        
    Returns:
        结构化的计划JSON，包含：
        - optimized_task: 优化后的任务描述
        - clarifying_questions: 澄清问题列表（如果有）
        - execution_steps: 详细执行步骤
        - tools_needed: 需要的工具列表
        - estimated_complexity: 预估复杂度
        - success_criteria: 成功标准
        
    Example:
        generate_smart_plan(analysis_json, "用户确认需要爬取手机类商品")
    """
    try:
        from langchain_openai import ChatOpenAI
        from ..core.config import config
        
        logger.info("Generating smart execution plan...")
        
        # 解析分析结果
        try:
            analysis_data = json.loads(analysis_result)
        except json.JSONDecodeError:
            logger.warning("Failed to parse analysis result, using raw text")
            analysis_data = {"raw_analysis": analysis_result}
        
        llm = ChatOpenAI(**config.get_llm_config())
        
        planning_prompt = f"""
作为智能执行规划专家，请基于分析结果生成详细的执行计划。

分析结果：
{json.dumps(analysis_data, ensure_ascii=False, indent=2)}

额外上下文：
{additional_context if additional_context else "无"}

请生成包含以下要素的完整执行计划：

1. **优化任务描述**：基于分析结果重新表述任务，更清晰具体
2. **澄清问题**：如果仍有模糊点，列出需要向用户确认的问题
3. **执行步骤**：详细的、可操作的步骤列表
4. **工具配置**：每个步骤需要的具体工具
5. **成功标准**：如何判断任务完成
6. **风险预案**：可能遇到的问题及解决方案

重要：你必须且只能输出JSON格式，不要包含任何其他文本、解释或markdown标记。

JSON输出模板：
{{
  "optimized_task": "优化后的清晰任务描述",
  "clarifying_questions": [
    "具体问题1？",
    "具体问题2？"
  ],
  "execution_steps": [
    {{
      "step_id": 1,
      "action": "具体操作描述",
      "tool": "使用的工具名称",
      "parameters": "工具参数",
      "expected_result": "预期结果",
      "validation": "验证方法"
    }}
  ],
  "tools_needed": [
    {{
      "tool_name": "browser_tools",
      "purpose": "浏览器操作和页面导航",
      "priority": "high"
    }}
  ],
  "estimated_duration": "预估时间",
  "success_criteria": [
    "成功标准1",
    "成功标准2"
  ],
  "risk_mitigation": [
    {{
      "risk": "可能的风险",
      "solution": "解决方案"
    }}
  ],
  "optimization_notes": "优化建议和注意事项"
}}

现在输出执行计划JSON：
{{"""
        
        response = llm.invoke(planning_prompt)
        plan_json = response.content.strip()
        
        # 确保JSON格式正确
        if not plan_json.startswith("{"):
            plan_json = "{" + plan_json
            
        # 验证JSON格式
        try:
            plan_result = json.loads(plan_json)
            steps_count = len(plan_result.get("execution_steps", []))
            logger.info(f"Smart plan generated successfully with {steps_count} steps")
            return plan_json
        except json.JSONDecodeError as e:
            logger.warning(f"JSON decode error in plan generation: {e}")
            
            # JSON修复策略
            import re
            brace_match = re.search(r"\{[\s\S]*\}", plan_json)
            if brace_match:
                try:
                    fixed_json = brace_match.group()
                    json.loads(fixed_json)  # 验证
                    return fixed_json
                except:
                    pass
            
            # 返回默认计划
            default_plan = {
                "optimized_task": analysis_data.get("intent", "执行用户任务"),
                "clarifying_questions": ["请提供更多具体信息"],
                "execution_steps": [
                    {
                        "step_id": 1,
                        "action": "分析并执行用户任务",
                        "tool": "browser_tools",
                        "parameters": "根据具体任务调整",
                        "expected_result": "任务完成",
                        "validation": "检查结果是否符合预期"
                    }
                ],
                "tools_needed": [
                    {
                        "tool_name": "browser_tools",
                        "purpose": "网页操作",
                        "priority": "high"
                    }
                ],
                "estimated_duration": "视任务复杂度而定",
                "success_criteria": ["达成用户目标"],
                "risk_mitigation": [
                    {
                        "risk": "信息不够明确",
                        "solution": "与用户进一步确认"
                    }
                ],
                "optimization_notes": "需要根据具体情况调整"
            }
            return json.dumps(default_plan, ensure_ascii=False)
            
    except Exception as e:
        logger.error(f"Failed to generate smart plan: {e}")
        error_plan = {
            "error": str(e),
            "optimized_task": "计划生成失败",
            "clarifying_questions": ["请重新描述任务"],
            "execution_steps": [],
            "tools_needed": [],
            "estimated_duration": "未知",
            "success_criteria": ["解决错误"],
            "risk_mitigation": [],
            "optimization_notes": f"错误信息: {str(e)}"
        }
        return json.dumps(error_plan, ensure_ascii=False)


@tool
def get_current_datetime_info(query: str = "") -> str:
    """
    获取当前日期时间信息，并智能解析相对时间表达。

    功能：
    - 获取当前日期和时间
    - 解析相对时间表达（如"明天"、"下周"、"上个月"等）
    - 转换为具体的日期时间
    - 支持多种日期格式输出

    Args:
        query: 时间查询字符串，可以是：
            - 空字符串：返回当前日期时间
            - "明天"、"后天"、"昨天"等相对日期
            - "下周"、"上周"、"下个月"等相对时间段
            - "今年"、"去年"、"明年"等年份
            - 或其他时间相关的自然语言表达

    Returns:
        包含具体日期时间信息的字符串，格式化为易读的形式

    Examples:
        get_current_datetime_info() -> "当前时间：2024-01-15 14:30:25，星期一"
        get_current_datetime_info("明天") -> "明天：2024-01-16，星期二"
        get_current_datetime_info("下周") -> "下周：2024-01-22 到 2024-01-28"
    """
    try:
        now = datetime.now()
        current_date = now.strftime("%Y-%m-%d")
        current_time = now.strftime("%H:%M:%S")
        current_weekday = now.strftime("%A")

        # 中文星期映射
        weekday_map = {
            "Monday": "星期一", "Tuesday": "星期二", "Wednesday": "星期三",
            "Thursday": "星期四", "Friday": "星期五", "Saturday": "星期六", "Sunday": "星期日"
        }
        current_weekday_cn = weekday_map.get(current_weekday, current_weekday)

        if not query.strip():
            return f"当前时间：{current_date} {current_time}，{current_weekday_cn}"

        query_lower = query.lower().strip()

        # 解析相对日期
        if any(word in query_lower for word in ["明天", "tomorrow"]):
            tomorrow = now + timedelta(days=1)
            tomorrow_weekday = weekday_map.get(tomorrow.strftime("%A"), tomorrow.strftime("%A"))
            return f"明天：{tomorrow.strftime('%Y-%m-%d')}，{tomorrow_weekday}"

        elif any(word in query_lower for word in ["后天", "day after tomorrow"]):
            day_after = now + timedelta(days=2)
            day_after_weekday = weekday_map.get(day_after.strftime("%A"), day_after.strftime("%A"))
            return f"后天：{day_after.strftime('%Y-%m-%d')}，{day_after_weekday}"

        elif any(word in query_lower for word in ["昨天", "yesterday"]):
            yesterday = now - timedelta(days=1)
            yesterday_weekday = weekday_map.get(yesterday.strftime("%A"), yesterday.strftime("%A"))
            return f"昨天：{yesterday.strftime('%Y-%m-%d')}，{yesterday_weekday}"

        elif any(word in query_lower for word in ["前天", "day before yesterday"]):
            day_before = now - timedelta(days=2)
            day_before_weekday = weekday_map.get(day_before.strftime("%A"), day_before.strftime("%A"))
            return f"前天：{day_before.strftime('%Y-%m-%d')}，{day_before_weekday}"

        elif any(word in query_lower for word in ["下周", "next week"]):
            next_week_start = now + timedelta(days=(7 - now.weekday()))
            next_week_end = next_week_start + timedelta(days=6)
            return f"下周：{next_week_start.strftime('%Y-%m-%d')} 到 {next_week_end.strftime('%Y-%m-%d')}"

        elif any(word in query_lower for word in ["上周", "last week"]):
            last_week_start = now - timedelta(days=(now.weekday() + 7))
            last_week_end = last_week_start + timedelta(days=6)
            return f"上周：{last_week_start.strftime('%Y-%m-%d')} 到 {last_week_end.strftime('%Y-%m-%d')}"

        elif any(word in query_lower for word in ["下个月", "next month"]):
            if now.month == 12:
                next_month = now.replace(year=now.year + 1, month=1, day=1)
            else:
                next_month = now.replace(month=now.month + 1, day=1)
            return f"下个月：{next_month.strftime('%Y-%m')} ({next_month.strftime('%Y年%m月')})"

        elif any(word in query_lower for word in ["上个月", "last month"]):
            if now.month == 1:
                last_month = now.replace(year=now.year - 1, month=12, day=1)
            else:
                last_month = now.replace(month=now.month - 1, day=1)
            return f"上个月：{last_month.strftime('%Y-%m')} ({last_month.strftime('%Y年%m月')})"

        elif any(word in query_lower for word in ["明年", "next year"]):
            next_year = now.year + 1
            return f"明年：{next_year}年"

        elif any(word in query_lower for word in ["去年", "last year"]):
            last_year = now.year - 1
            return f"去年：{last_year}年"

        elif any(word in query_lower for word in ["今年", "this year"]):
            return f"今年：{now.year}年"

        elif any(word in query_lower for word in ["本周", "this week"]):
            week_start = now - timedelta(days=now.weekday())
            week_end = week_start + timedelta(days=6)
            return f"本周：{week_start.strftime('%Y-%m-%d')} 到 {week_end.strftime('%Y-%m-%d')}"

        elif any(word in query_lower for word in ["本月", "this month"]):
            month_start = now.replace(day=1)
            if now.month == 12:
                next_month_start = now.replace(year=now.year + 1, month=1, day=1)
            else:
                next_month_start = now.replace(month=now.month + 1, day=1)
            month_end = next_month_start - timedelta(days=1)
            return f"本月：{month_start.strftime('%Y-%m-%d')} 到 {month_end.strftime('%Y-%m-%d')} ({now.strftime('%Y年%m月')})"

        else:
            # 如果无法解析，返回当前时间和查询信息
            return f"当前时间：{current_date} {current_time}，{current_weekday_cn}。查询 '{query}' 无法解析，请使用如：明天、下周、上个月等表达。"

    except Exception as e:
        logger.error(f"Failed to get datetime info: {e}")
        return f"获取时间信息失败：{str(e)}"


# 导入现有的搜索工具
def _get_web_search_tool():
    """获取网络搜索工具"""
    try:
        from .search_tools import web_search_serp
        return web_search_serp
    except ImportError as e:
        logger.error(f"Failed to import search tool: {e}")
        
        # 创建一个备用的搜索工具
        @tool
        def backup_web_search(query: str, num_results: int = 5) -> str:
            """备用网络搜索工具（当search_tools不可用时）"""
            return f"搜索功能暂时不可用。查询：{query}"
        
        return backup_web_search


# Smart Planner工具列表
def get_smart_planner_tools() -> List[BaseTool]:
    """
    获取SmartPlannerAgent的所有工具。

    Returns:
        包含4个核心工具的列表：
        1. understand_and_analyze - 统一理解和分析
        2. generate_smart_plan - 智能计划生成
        3. get_current_datetime_info - 日期时间查询和转换
        4. web_search_serp - 网络搜索（从search_tools导入）
    """
    tools = [
        understand_and_analyze,
        generate_smart_plan,
        get_current_datetime_info,
        _get_web_search_tool(),
    ]

    logger.info(f"SmartPlannerAgent tools loaded: {[tool.name for tool in tools]}")
    return tools


# 工具列表常量（用于兼容现有代码）
SMART_PLANNER_TOOLS = [
    understand_and_analyze,
    generate_smart_plan,
    get_current_datetime_info,
    _get_web_search_tool(),
]


# 工具说明和使用示例
TOOL_DESCRIPTIONS = {
    "understand_and_analyze": {
        "purpose": "深度分析用户输入，识别意图、复杂度和模糊点",
        "input": "用户的原始任务描述",
        "output": "结构化分析结果JSON",
        "example": "understand_and_analyze('爬取京东手机数据')"
    },
    "generate_smart_plan": {
        "purpose": "基于分析结果生成详细的执行计划",
        "input": "分析结果JSON + 额外上下文",
        "output": "包含步骤、工具、成功标准的执行计划JSON",
        "example": "generate_smart_plan(analysis_json, '用户确认需要前20个商品')"
    },
    "get_current_datetime_info": {
        "purpose": "获取当前日期时间信息，解析相对时间表达",
        "input": "时间查询字符串（如'明天'、'下周'等）",
        "output": "具体的日期时间信息",
        "example": "get_current_datetime_info('明天') -> '明天：2024-01-16，星期二'"
    },
    "web_search_serp": {
        "purpose": "搜索网络信息辅助规划决策",
        "input": "搜索查询字符串",
        "output": "格式化的搜索结果",
        "example": "web_search_serp('京东官网域名')"
    }
}


def get_tool_info() -> Dict[str, Any]:
    """
    获取工具信息和使用说明。
    
    Returns:
        包含所有工具详细信息的字典
    """
    return {
        "total_tools": len(SMART_PLANNER_TOOLS),
        "tool_names": [tool.name for tool in SMART_PLANNER_TOOLS],
        "descriptions": TOOL_DESCRIPTIONS,
        "workflow": [
            "1. 使用understand_and_analyze分析用户输入",
            "2. 根据需要使用web_search_serp获取外部信息",
            "3. 使用generate_smart_plan生成执行计划",
            "4. 根据反馈迭代优化计划"
        ]
    }


if __name__ == "__main__":
    # 测试工具功能
    tools = get_smart_planner_tools()
    print(f"Loaded {len(tools)} smart planner tools:")
    for tool in tools:
        print(f"- {tool.name}: {tool.description}")