"""
代码生成工具模块 - 精简版三工具设计

提供从WebAgent执行历史生成Playwright代码的精简工具集。
只包含三个核心工具：生成、验证修复、保存。
"""

import json
import logging
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

logger = logging.getLogger(__name__)


@tool
def extract_and_generate_code(execution_results: str, style: str = "async") -> str:
    """
    从执行历史直接生成Playwright代码。
    
    一步完成提取操作序列并生成可执行的Playwright Python代码。
    
    Args:
        execution_results: WebAgent的执行历史（JSON字符串）
        style: 代码风格 "async" 或 "sync"，默认 "async"
        
    Returns:
        完整的可执行Python代码字符串
        
    Example:
        code = extract_and_generate_code(execution_results, "async")
    """
    try:
        logger.info(f"开始生成{style}风格的Playwright代码")
        
        # 解析执行历史
        try:
            if isinstance(execution_results, str):
                data = json.loads(execution_results)
            else:
                data = execution_results
        except json.JSONDecodeError:
            # 如果不是JSON，让LLM处理
            data = execution_results
        
        # 初始化LLM
        llm = ChatOpenAI(
            model=os.getenv("OPENAI_MODEL", "gpt-4-turbo"),
            temperature=0.2
        )
        
        # 构建提示
        system_prompt = f"""你是Playwright代码生成专家。
基于WebAgent的执行历史生成可执行的Python代码。

执行历史包含已完成的操作序列，每个操作有：
- tool_name: 工具名称（navigate_browser, click_element, type_text等）
- parameters: 参数（url, element_selector, text等）
- element_selector都是XPath格式（html/body/...）

生成要求：
1. 使用{'async' if style == 'async' else 'sync'} Playwright API
2. XPath选择器需要添加'xpath='前缀
3. 包含错误处理和重试机制（每个操作最多重试3次）
4. 包含资源清理（finally块）
5. 添加适当的等待（page.wait_for_load_state('load')）
6. 生成完整可运行的脚本

代码结构：
```python
{'import asyncio' if style == 'async' else ''}
from playwright.{'async_' if style == 'async' else 'sync_'}api import {'async_' if style == 'async' else ''}playwright
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

{'async ' if style == 'async' else ''}def main():
    # 完整的自动化代码
    pass

if __name__ == "__main__":
    {'asyncio.run(main())' if style == 'async' else 'main()'}
```
"""

        human_prompt = f"""请基于以下执行历史生成Playwright代码：

{json.dumps(data, ensure_ascii=False, indent=2) if isinstance(data, (dict, list)) else str(data)[:5000]}

生成完整的、可直接运行的Python脚本。"""
        
        # 调用LLM生成代码
        response = llm.invoke([
            SystemMessage(content=system_prompt),
            HumanMessage(content=human_prompt)
        ])
        
        # 提取代码
        code = response.content
        
        # 如果返回包含markdown代码块，提取代码
        if "```python" in code:
            code = code.split("```python")[1].split("```")[0]
        elif "```" in code:
            code = code.split("```")[1].split("```")[0]
        
        logger.info(f"成功生成{style}风格的Playwright代码")
        return code.strip()
        
    except Exception as e:
        logger.error(f"代码生成失败: {e}")
        return f"# 代码生成失败\n# 错误: {str(e)}"


@tool
def yolo_validate_and_fix(code: str, max_iterations: int = 3) -> str:
    """
    YOLO模式 - 执行代码并自动修复错误。
    
    执行生成的代码，如果失败则分析错误并修复，持续迭代直到成功。
    
    Args:
        code: 要验证和修复的Python代码
        max_iterations: 最大迭代次数，默认3次
        
    Returns:
        JSON字符串，包含：
        - success: 是否成功
        - final_code: 最终代码
        - iterations: 迭代次数
        - errors: 遇到的错误列表
        
    Example:
        result = yolo_validate_and_fix(code, 3)
    """
    try:
        logger.info(f"开始YOLO模式验证和修复，最大迭代{max_iterations}次")
        
        current_code = code
        errors_encountered = []
        
        # 进行迭代验证和修复
        for iteration in range(max_iterations):
            logger.info(f"YOLO迭代 {iteration + 1}/{max_iterations}")
            
            # 创建临时文件
            temp_file = Path(f"temp_yolo_test_{iteration}.py")
            try:
                # 保存代码到临时文件
                temp_file.write_text(current_code, encoding='utf-8')
                
                # 执行代码
                result = subprocess.run(
                    [sys.executable, str(temp_file)],
                    capture_output=True,
                    text=True,
                    timeout=30,
                    env={**os.environ, "HEADLESS": "true"}  # 强制无头模式测试
                )
                
                if result.returncode == 0:
                    logger.info(f"✅ 代码验证成功！（第{iteration + 1}次尝试）")
                    return json.dumps({
                        "success": True,
                        "final_code": current_code,
                        "iterations": iteration + 1,
                        "errors": errors_encountered
                    }, ensure_ascii=False, indent=2)
                
                # 执行失败，分析错误
                error_msg = result.stderr or result.stdout
                errors_encountered.append({
                    "iteration": iteration + 1,
                    "error": error_msg[:1000]  # 限制错误信息长度
                })
                
                logger.warning(f"执行失败，尝试修复错误：{error_msg[:200]}")
                
                # 使用LLM修复代码
                if iteration < max_iterations - 1:  # 还有重试机会
                    llm = ChatOpenAI(
                        model=os.getenv("OPENAI_MODEL", "gpt-4-turbo"),
                        temperature=0.1  # 低温度确保稳定修复
                    )
                    
                    fix_prompt = f"""修复以下Playwright代码的错误：

原始代码：
```python
{current_code}
```

错误信息：
```
{error_msg[:2000]}
```

请修复错误并返回完整的可运行代码。常见修复：
1. TimeoutError → 增加超时时间，使用wait_for_load_state('load')而非'networkidle'
2. 选择器找不到 → 添加xpath=前缀，增加等待时间
3. 导入错误 → 确保所有必要的导入
4. 异步错误 → 确保async/await使用正确

只返回修复后的代码，不要解释。"""
                    
                    response = llm.invoke([HumanMessage(content=fix_prompt)])
                    
                    # 提取修复后的代码
                    fixed_code = response.content
                    if "```python" in fixed_code:
                        fixed_code = fixed_code.split("```python")[1].split("```")[0]
                    elif "```" in fixed_code:
                        fixed_code = fixed_code.split("```")[1].split("```")[0]
                    
                    current_code = fixed_code.strip()
                
            except subprocess.TimeoutExpired:
                logger.warning(f"执行超时（30秒）")
                errors_encountered.append({
                    "iteration": iteration + 1,
                    "error": "Execution timeout (30s)"
                })
                
                # 为超时问题添加更快的执行
                if iteration < max_iterations - 1:
                    # 简单地减少等待时间
                    current_code = current_code.replace("networkidle", "load")
                    current_code = current_code.replace("timeout=30000", "timeout=10000")
                    
            except Exception as e:
                logger.error(f"YOLO验证出错: {e}")
                errors_encountered.append({
                    "iteration": iteration + 1,
                    "error": str(e)
                })
                
            finally:
                # 清理临时文件
                if temp_file.exists():
                    temp_file.unlink()
        
        # 达到最大迭代次数
        logger.warning(f"达到最大迭代次数({max_iterations})，返回最后版本的代码")
        return json.dumps({
            "success": False,
            "final_code": current_code,
            "iterations": max_iterations,
            "errors": errors_encountered
        }, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"YOLO验证失败: {e}")
        return json.dumps({
            "success": False,
            "final_code": code,
            "iterations": 0,
            "errors": [{"error": str(e)}]
        }, ensure_ascii=False)


@tool
def save_generated_code(code: str, filename: str = "") -> str:
    """
    保存代码到generated_code/目录。
    
    自动创建目录并生成唯一文件名。
    
    Args:
        code: 要保存的Python代码
        filename: 可选的文件名，不提供时自动生成带时间戳的文件名
        
    Returns:
        保存的文件路径字符串
        
    Example:
        path = save_generated_code(code, "booking_automation.py")
    """
    try:
        logger.info("开始保存生成的代码")
        
        # 确定项目根目录
        current_dir = Path(__file__).parent
        project_root = current_dir.parent.parent.parent
        generated_code_dir = project_root / "generated_code"
        
        # 创建目录（如果不存在）
        generated_code_dir.mkdir(exist_ok=True)
        logger.info(f"代码保存目录: {generated_code_dir}")
        
        # 生成文件名
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"playwright_automation_{timestamp}.py"
        elif not filename.endswith(".py"):
            filename += ".py"
        
        # 确保文件名唯一
        file_path = generated_code_dir / filename
        counter = 1
        original_filename = filename
        while file_path.exists():
            name_part = original_filename.replace(".py", "")
            filename = f"{name_part}_{counter}.py"
            file_path = generated_code_dir / filename
            counter += 1
        
        # 保存代码
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(code)
        
        logger.info(f"代码已保存到: {file_path}")
        
        # 返回相对于项目根目录的路径
        relative_path = file_path.relative_to(project_root)
        return str(relative_path)
        
    except Exception as e:
        logger.error(f"保存代码失败: {e}")
        return f"保存失败：{str(e)}"


# 工具列表 - 精简为3个核心工具
CODEGEN_TOOLS = [
    extract_and_generate_code,
    yolo_validate_and_fix,
    save_generated_code,
]


def get_codegen_tools():
    """
    获取代码生成工具列表。
    
    Returns:
        包含所有代码生成工具的列表
    """
    return CODEGEN_TOOLS.copy()