"""Singleton browser worker thread.

All blocking Playwright sync calls (navigate, screenshot, etc.) **must** run on the
same OS thread – Playwright objects are not thread-safe.  This module provides a
`run_browser_call()` helper that executes any callable in that dedicated thread
and returns its result, propagating exceptions.

Usage (sync tool example):

    from ..core.browser_thread import run_browser_call
    from ..core.browser import get_global_browser

    def navigate(url: str):
        run_browser_call(lambda: get_global_browser().navigate(url))

Only this file creates/manages the thread, so future tools or agents can simply
import and reuse `run_browser_call`, achieving minimal code changes while
ensuring thread-safety.
"""

from __future__ import annotations

import queue
import threading
from types import TracebackType
from typing import Any, Callable, Optional, Type

# Task and result queues
_task_q: queue.Queue[tuple[Callable[[], Any], "_Marker"]] = queue.Queue()
_result_q: queue.Queue[tuple[bool, Any]] = queue.Queue()


# Marker object to signal shutdown – not used now but kept for completeness
class _Marker:  # noqa: D401 – marker class
    """Sentinel for worker termination."""


def _worker() -> None:
    """Background thread target – executes callables in FIFO order."""
    while True:
        func, marker = _task_q.get()
        if isinstance(marker, _Marker):  # graceful shutdown signal
            break
        try:
            result = func()
            _result_q.put((True, result))
        except Exception as exc:  # pragma: no cover
            _result_q.put((False, exc))


# Launch the thread eagerly so first call doesn't pay startup cost
_thread = threading.Thread(target=_worker, name="BrowserWorker", daemon=True)
_thread.start()


def run_browser_call(func: Callable[[], Any]) -> Any:  # noqa: D401 – simple helper
    """Run *func* directly in current thread to avoid greenlet threading issues.

    Previously used dedicated browser worker thread but this caused greenlet
    switching errors with LangGraph. Now executes directly for simplicity.
    """
    # 直接执行，避免greenlet线程切换错误
    return func()


# Optional: public alias for external importers
__all__ = ["run_browser_call"]
