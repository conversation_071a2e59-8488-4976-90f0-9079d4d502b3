"""
Smart Planner Agent module for unified task planning and prompt optimization.

This module provides a specialized agent that combines the capabilities of 
task planning and prompt optimization. It analyzes user intent, generates
clarifying questions, and creates optimized execution plans.
"""

import logging
from typing import Optional, List, Dict, Any
from .prompts import get_smart_planner_prompt
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain_openai import ChatOpenAI
from langchain_core.tools import BaseTool

from ..core.config import config

logger = logging.getLogger(__name__)


class SmartPlannerAgent:
    """
    统一的智能规划Agent，整合了提示词优化和任务规划功能。

    特性：
    - 使用专业的智能规划工具集
    - 支持任务分析和执行计划生成
    - 集成搜索能力获取实时信息
    - 提供状态管理和历史记录
    """

    def __init__(
        self,
        tools: Optional[List[BaseTool]] = None,
        verbose: Optional[bool] = None,
        llm_config: Optional[dict] = None,
    ):
        """
        Initialize the SmartPlannerAgent.

        Args:
            tools: List of LangChain tools to use. If None, uses default smart planner tools.
            verbose: Whether to enable verbose logging.
            llm_config: Custom LLM configuration.
        """
        if tools is None:
            from ..tools.smart_planner_tools import get_smart_planner_tools
            self.tools = get_smart_planner_tools()
            logger.info(f"SmartPlannerAgent initialized with {len(self.tools)} tools: {[t.name for t in self.tools]}")
        else:
            self.tools = tools

        self.verbose = verbose if verbose is not None else config.VERBOSE
        self.llm_config = llm_config or config.get_llm_config()

        # Lazy initialization
        self._llm = None
        self._agent = None
        self._executor = None
        
        # 内部状态管理（保留用于状态查询方法）
        self._last_analysis = None
        self._last_plan = None
        self._conversation_history = []

    def _create_llm(self) -> ChatOpenAI:
        """Create and configure the LLM instance."""
        if self._llm is None:
            try:
                self._llm = ChatOpenAI(**self.llm_config)
                logger.info(f"SmartPlannerAgent LLM created with model: {self.llm_config.get('model')}")
            except Exception as e:
                logger.error(f"Failed to create LLM: {e}")
                raise
        return self._llm

    def _create_agent(self) -> None:
        """Create the LangChain agent."""
        if self._agent is None:
            try:
                llm = self._create_llm()
                prompt = get_smart_planner_prompt()
                self._agent = create_openai_functions_agent(llm, self.tools, prompt)
                logger.info("SmartPlannerAgent created successfully")
            except Exception as e:
                logger.error(f"Failed to create SmartPlannerAgent: {e}")
                raise

    def _create_executor(self) -> AgentExecutor:
        """Create the agent executor."""
        if self._executor is None:
            self._create_agent()
            try:
                self._executor = AgentExecutor(
                    agent=self._agent,
                    tools=self.tools,
                    verbose=self.verbose,
                    max_iterations=15,  # 稍微增加，因为整合了更多功能
                    handle_parsing_errors=True,
                    return_intermediate_steps=True,  # 返回中间步骤便于调试
                )
                logger.info("SmartPlannerAgent executor created successfully")
            except Exception as e:
                logger.error(f"Failed to create SmartPlannerAgent executor: {e}")
                raise
        return self._executor

    def invoke(self, input_text: str, context: Optional[str] = None) -> dict:
        """
        调用Agent执行智能规划任务。

        Args:
            input_text: 用户输入的任务描述
            context: 额外的上下文信息

        Returns:
            包含分析和计划结果的字典
        """
        executor = self._create_executor()

        try:
            logger.info(f"SmartPlannerAgent processing: {input_text[:100]}...")
            
            # 构建输入上下文
            full_input = input_text
            if context:
                full_input += f"\n\n额外上下文：{context}"
            
            # 记录对话历史
            self._conversation_history.append({
                "type": "user_input",
                "content": input_text,
                "context": context,
                "timestamp": "unknown"
            })
            
            result = executor.invoke({"input": full_input})
            
            # 记录结果
            self._conversation_history.append({
                "type": "agent_output",
                "content": result.get("output", ""),
                "intermediate_steps": len(result.get("intermediate_steps", [])),
                "timestamp": "unknown"
            })
            
            logger.info("SmartPlannerAgent processing completed successfully")
            
            # 增强结果信息
            enhanced_result = result.copy()
            enhanced_result.update({
                "agent_type": "SmartPlannerAgent",
                "analysis_available": self._last_analysis is not None,
                "plan_available": self._last_plan is not None,
                "tools_used": [step[0].tool for step in result.get("intermediate_steps", []) if len(step) > 0],
                "conversation_length": len(self._conversation_history)
            })
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"SmartPlannerAgent processing failed: {e}")
            error_result = {
                "error": str(e),
                "agent_type": "SmartPlannerAgent",
                "input": input_text,
                "context": context
            }
            
            # 记录错误
            self._conversation_history.append({
                "type": "error",
                "content": str(e),
                "input": input_text,
                "timestamp": "unknown"
            })
            
            return error_result

    def get_analysis(self) -> Optional[Dict[str, Any]]:
        """获取最近的任务分析结果。"""
        return self._last_analysis

    def get_plan(self) -> Optional[Dict[str, Any]]:
        """获取最近的执行计划。"""
        return self._last_plan

    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史记录。"""
        return self._conversation_history.copy()

    def clear_history(self) -> None:
        """清除对话历史和缓存。"""
        self._conversation_history = []
        self._last_analysis = None
        self._last_plan = None
        logger.info("SmartPlannerAgent history and cache cleared")

    def get_status(self) -> Dict[str, Any]:
        """
        获取Agent状态信息。
        
        Returns:
            包含Agent状态的字典
        """
        return {
            "agent_type": "SmartPlannerAgent",
            "tools_count": len(self.tools),
            "tools": [tool.name for tool in self.tools],
            "llm_model": self.llm_config.get("model", "unknown"),
            "verbose": self.verbose,
            "has_analysis": self._last_analysis is not None,
            "has_plan": self._last_plan is not None,
            "conversation_items": len(self._conversation_history),
            "initialized": {
                "llm": self._llm is not None,
                "agent": self._agent is not None,
                "executor": self._executor is not None
            }
        }

    def cleanup(self) -> None:
        """Clean up resources used by the agent."""
        try:
            self._llm = None
            self._agent = None
            self._executor = None
            self._last_analysis = None
            self._last_plan = None
            self._conversation_history = []
            logger.info("SmartPlannerAgent cleanup completed")
        except Exception as e:
            logger.error(f"Error during SmartPlannerAgent cleanup: {e}")


def build_smart_planner_agent(
    tools: Optional[List[BaseTool]] = None,
    verbose: Optional[bool] = None,
    llm_config: Optional[dict] = None,
) -> SmartPlannerAgent:
    """
    Build and return a configured SmartPlannerAgent instance.

    Args:
        tools: List of LangChain tools to use
        verbose: Whether to enable verbose logging
        llm_config: Custom LLM configuration

    Returns:
        A configured SmartPlannerAgent instance
        
    Example:
        >>> agent = build_smart_planner_agent(verbose=True)
        >>> result = agent.invoke("爬取京东手机商品数据")
        >>> analysis = agent.get_analysis()
        >>> plan = agent.get_plan()
    """
    try:
        agent = SmartPlannerAgent(tools=tools, verbose=verbose, llm_config=llm_config)
        logger.info("SmartPlannerAgent built successfully")
        return agent
    except Exception as e:
        logger.error(f"Failed to build SmartPlannerAgent: {e}")
        raise


if __name__ == "__main__":
    # 测试SmartPlannerAgent
    print("Testing SmartPlannerAgent...")

    # 完整测试
    test_input = "爬取淘宝商品数据"
    agent = build_smart_planner_agent(verbose=True)
    result = agent.invoke(test_input)
    print(f"Full result: {result}")
    print(f"Agent status: {agent.get_status()}")

    agent.cleanup()